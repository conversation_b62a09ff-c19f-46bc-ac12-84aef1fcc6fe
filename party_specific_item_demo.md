# Party Specific Item - Inclusive/Exclusive Functionality with Group Support

## Overview

The Party Specific Item doctype has been enhanced with:
1. A new "Select" field that allows you to choose between **Inclusive** and **Exclusive** filtering modes
2. Support for **Customer Group** and **Supplier Group** filtering in addition to individual customers and suppliers

## How it Works

### Inclusive Mode (Default)
- **Behavior**: Shows ONLY the items specified in the party specific item rules
- **Use Case**: When you want to restrict a customer/supplier to see only specific items
- **Example**: Customer A can only see Item X and Item Y

### Exclusive Mode
- **Behavior**: Shows ALL items EXCEPT the ones specified in the party specific item rules
- **Use Case**: When you want to hide specific items from a customer/supplier
- **Example**: Customer B can see all items except Item Z

## Field Details

### Select Field
- **Field Name**: `select`
- **Field Type**: Select
- **Options**:
  - Inclusive (Default)
  - Exclusive
- **Default Value**: Inclusive (maintains backward compatibility)

### Party Type Field (Enhanced)
- **Field Name**: `party_type`
- **Field Type**: Select
- **Options**:
  - Customer
  - Customer Group (NEW)
  - Supplier
  - Supplier Group (NEW)

## Group-Based Filtering

### How Group Filtering Works
When a customer or supplier is selected in an item query, the system now checks for restrictions at two levels:

1. **Direct Party Rules**: Rules specifically created for that customer/supplier
2. **Group Rules**: Rules created for the customer group or supplier group that the party belongs to

Both types of rules are applied together, providing flexible multi-level filtering.

## Implementation Details

### Database Changes
- Added `select` field to Party Specific Item doctype
- Default value set to "Inclusive" for backward compatibility
- Updated validation to include the select field in uniqueness check

### Code Changes
1. **JSON Configuration** (`party_specific_item.json`):
   - Added select field with default value "Inclusive"

2. **Python Model** (`party_specific_item.py`):
   - Updated type annotations
   - Enhanced validation logic

3. **Query Logic** (`controllers/queries.py`):
   - Modified `item_query` function to handle both inclusive and exclusive filters
   - Separate handling for inclusive (`in`) and exclusive (`not in`) filters
   - Backward compatibility maintained with default "Inclusive" behavior

4. **Test Cases** (`test_party_specific_item.py`):
   - Added tests for both inclusive and exclusive functionality
   - Updated helper function to support select parameter

## Usage Examples

### Example 1: Inclusive Filter
```python
# Create a party specific item with inclusive filter
psi = frappe.get_doc({
    "doctype": "Party Specific Item",
    "party_type": "Customer",
    "party": "Customer A",
    "restrict_based_on": "Item",
    "based_on_value": "Item-001",
    "select": "Inclusive"  # Only show Item-001
})
```

### Example 2: Exclusive Filter
```python
# Create a party specific item with exclusive filter
psi = frappe.get_doc({
    "doctype": "Party Specific Item",
    "party_type": "Customer", 
    "party": "Customer B",
    "restrict_based_on": "Item",
    "based_on_value": "Item-002",
    "select": "Exclusive"  # Show all items except Item-002
})
```

### Example 3: Multiple Inclusive Filters
```python
# Multiple inclusive filters work together (OR logic)
# Customer will see Item-001 OR Item-002
psi1 = frappe.get_doc({
    "doctype": "Party Specific Item",
    "party_type": "Customer",
    "party": "Customer C",
    "restrict_based_on": "Item",
    "based_on_value": "Item-001",
    "select": "Inclusive"
})

psi2 = frappe.get_doc({
    "doctype": "Party Specific Item",
    "party_type": "Customer",
    "party": "Customer C",
    "restrict_based_on": "Item",
    "based_on_value": "Item-002",
    "select": "Inclusive"
})
```

### Example 4: Customer Group Inclusive Filter
```python
# Create a party specific item for customer group
psi = frappe.get_doc({
    "doctype": "Party Specific Item",
    "party_type": "Customer Group",
    "party": "Retail Customers",
    "restrict_based_on": "Item Group",
    "based_on_value": "Consumer Products",
    "select": "Inclusive"  # Only show Consumer Products to Retail Customers
})
```

### Example 5: Supplier Group Exclusive Filter
```python
# Create a party specific item for supplier group
psi = frappe.get_doc({
    "doctype": "Party Specific Item",
    "party_type": "Supplier Group",
    "party": "Local Suppliers",
    "restrict_based_on": "Item",
    "based_on_value": "Imported-Item-001",
    "select": "Exclusive"  # Hide imported items from local suppliers
})
```

### Example 6: Mixed Individual and Group Rules
```python
# Group rule: All customers in "VIP Customers" group can only see premium items
group_rule = frappe.get_doc({
    "doctype": "Party Specific Item",
    "party_type": "Customer Group",
    "party": "VIP Customers",
    "restrict_based_on": "Item Group",
    "based_on_value": "Premium Products",
    "select": "Inclusive"
})

# Individual rule: Specific customer cannot see a particular premium item
individual_rule = frappe.get_doc({
    "doctype": "Party Specific Item",
    "party_type": "Customer",
    "party": "Customer-VIP-001",
    "restrict_based_on": "Item",
    "based_on_value": "Premium-Item-Restricted",
    "select": "Exclusive"
})
# Result: Customer-VIP-001 will see all premium products except Premium-Item-Restricted
```

## Testing Results

✅ **Individual Customer/Supplier Inclusive**: Successfully shows only specified items
✅ **Individual Customer/Supplier Exclusive**: Successfully hides specified items and shows all others
✅ **Customer Group Inclusive**: Successfully applies group-level inclusive filtering
✅ **Customer Group Exclusive**: Successfully applies group-level exclusive filtering
✅ **Supplier Group Inclusive**: Successfully applies group-level inclusive filtering
✅ **Supplier Group Exclusive**: Successfully applies group-level exclusive filtering
✅ **Multiple Filters**: Multiple inclusive/exclusive filters work correctly
✅ **Mixed Individual and Group Rules**: Both individual and group rules apply together
✅ **Backward Compatibility**: Existing records work without modification
✅ **Database Migration**: Successfully migrated existing installations

## Benefits

1. **Multi-Level Filtering**: Support for both individual party and group-based filtering
2. **Flexibility**: Choose between showing only specific items or hiding specific items
3. **Hierarchical Control**: Group rules provide broad control, individual rules provide fine-tuning
4. **Backward Compatibility**: Existing functionality remains unchanged
5. **User-Friendly**: Simple select field with clear options
6. **Comprehensive**: Works with all restriction types (Item, Item Group, Brand)
7. **Efficient**: Optimized query logic for both individual and group-based filtering
8. **Scalable**: Easy to manage large numbers of customers/suppliers through group rules

## Use Cases

### Retail Business
- **Customer Groups**: "Wholesale Customers" see wholesale items, "Retail Customers" see retail items
- **Exclusive Items**: Hide competitor products from certain customer groups

### Manufacturing
- **Supplier Groups**: "Local Suppliers" cannot see imported raw materials
- **Quality Tiers**: "Premium Suppliers" see high-grade materials, others see standard materials

### Distribution
- **Regional Groups**: Different customer groups see region-specific products
- **Channel Partners**: Different partner types see different product catalogs
