exclude: 'node_modules|.git'
default_stages: [pre-commit]
fail_fast: false


repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      - id: trailing-whitespace
        files: "frappe.*"
        exclude: ".*json$|.*txt$|.*csv|.*md|.*svg"
      - id: check-yaml
      - id: no-commit-to-branch
        args: ['--branch', 'develop']
      - id: check-merge-conflict
      - id: check-ast
      - id: check-json
      - id: check-toml
      - id: check-yaml
      - id: debug-statements

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.13.2
    hooks:
      - id: ruff
        name: "Run ruff linter and apply fixes"
        args: ["--fix"]

      - id: ruff-format
        name: "Format Python code"

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v2.7.1
    hooks:
      - id: prettier
        types_or: [javascript, vue, scss]
        # Ignore any files that might contain jinja / bundles
        exclude: |
            (?x)^(
                frappe/public/dist/.*|
                .*node_modules.*|
                .*boilerplate.*|
                frappe/www/website_script.js|
                frappe/templates/includes/.*|
                frappe/public/js/lib/.*|
                frappe/website/doctype/website_theme/website_theme_template.scss
            )$


  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.44.0
    hooks:
      - id: eslint
        types_or: [javascript]
        args: ['--quiet']
        # Ignore any files that might contain jinja / bundles
        exclude: |
            (?x)^(
                frappe/public/dist/.*|
                cypress/.*|
                .*node_modules.*|
                .*boilerplate.*|
                frappe/www/website_script.js|
                frappe/templates/includes/.*|
                frappe/public/js/lib/.*
            )$

ci:
    autoupdate_schedule: weekly
    skip: []
    submodules: false
