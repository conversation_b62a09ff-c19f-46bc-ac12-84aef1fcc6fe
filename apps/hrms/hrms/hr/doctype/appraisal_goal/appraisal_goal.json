{"allow_copy": 0, "allow_import": 0, "allow_rename": 0, "autoname": "hash", "beta": 0, "creation": "2013-02-22 01:27:44", "custom": 0, "docstatus": 0, "doctype": "DocType", "editable_grid": 1, "fields": [{"allow_on_submit": 0, "bold": 0, "collapsible": 0, "description": "Key Responsibility Area", "fieldname": "kra", "fieldtype": "Small Text", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 1, "label": "Goal", "length": 0, "no_copy": 0, "oldfieldname": "kra", "oldfieldtype": "Small Text", "permlevel": 0, "print_hide": 0, "print_hide_if_no_value": 0, "print_width": "240px", "read_only": 0, "report_hide": 0, "reqd": 1, "search_index": 0, "set_only_once": 0, "unique": 0, "width": "240px"}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "section_break_2", "fieldtype": "Section Break", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "length": 0, "no_copy": 0, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "per_weightage", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 1, "label": "Weightage (%)", "length": 0, "no_copy": 0, "oldfieldname": "per_weightage", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "permlevel": 0, "print_hide": 0, "print_hide_if_no_value": 0, "print_width": "70px", "read_only": 0, "report_hide": 0, "reqd": 1, "search_index": 0, "set_only_once": 0, "unique": 0, "width": "70px"}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "column_break_4", "fieldtype": "Column Break", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "length": 0, "no_copy": 0, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "score", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 1, "label": "Score", "length": 0, "no_copy": 1, "oldfieldname": "score", "oldfieldtype": "Select", "options": "", "permlevel": 0, "print_hide": 0, "print_hide_if_no_value": 0, "print_width": "70px", "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0, "width": "70px"}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "section_break_6", "fieldtype": "Section Break", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "length": 0, "no_copy": 0, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "score_earned", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 1, "label": "Score Earned", "length": 0, "no_copy": 1, "oldfieldname": "score_earned", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "permlevel": 0, "print_hide": 0, "print_hide_if_no_value": 0, "print_width": "70px", "read_only": 1, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0, "width": "70px"}], "hide_heading": 0, "hide_toolbar": 0, "idx": 1, "image_view": 0, "in_create": 0, "is_submittable": 0, "issingle": 0, "istable": 1, "max_attachments": 0, "modified": "2025-09-24 15:39:16.891078", "modified_by": "Administrator", "module": "HR", "name": "Appraisal Goal", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "quick_entry": 0, "read_only": 0, "read_only_onload": 0, "track_seen": 0}