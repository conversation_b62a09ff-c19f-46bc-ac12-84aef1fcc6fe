{"aggregate_function_based_on": "", "creation": "2022-08-29 23:28:14.869725", "docstatus": 0, "doctype": "Number Card", "document_type": "Job Requisition", "dynamic_filters_json": "[]", "filters_config": "[{\n\tfieldname: \"company\",\n\tlabel: __(\"Company\"),\n\tfieldtype: \"Link\",\n\toptions: \"Company\",\n\tdefault: frappe.defaults.get_user_default(\"Company\")\n},\n{\n\tfieldname: \"department\",\n\tlabel: __(\"Department\"),\n\tfieldtype: \"Link\",\n\toptions: \"Department\"\n},\n{\n    fieldname: \"designation\",\n\tlabel: __(\"Designation\"),\n\tfieldtype: \"Link\",\n\toptions: \"Designation\"\n}]", "filters_json": "[]", "function": "Count", "idx": 0, "is_public": 1, "is_standard": 1, "label": "Time to Fill", "method": "hrms.hr.doctype.job_requisition.job_requisition.get_avg_time_to_fill", "modified": "2025-09-17 19:05:34.012311", "modified_by": "Administrator", "module": "HR", "name": "Time to Fill", "owner": "Administrator", "parent_document_type": "", "report_function": "Sum", "show_full_number": 0, "show_percentage_stats": 1, "stats_time_interval": "Daily", "type": "Custom"}