# Copyright (c) 2021, Frappe Technologies Pvt. Ltd. and Contributors
# See license.txt

import frappe
from frappe.tests.utils import FrappeTestCase

from erpnext.controllers.queries import item_query

test_dependencies = ["Item", "Customer", "Supplier", "Customer Group", "Supplier Group"]


def create_party_specific_item(**args):
	psi = frappe.new_doc("Party Specific Item")
	psi.party_type = args.get("party_type")
	psi.party = args.get("party")
	psi.restrict_based_on = args.get("restrict_based_on")
	psi.based_on_value = args.get("based_on_value")
	psi.select = args.get("select", "Inclusive")  # Default to Inclusive
	psi.insert()


class TestPartySpecificItem(FrappeTestCase):
	def setUp(self):
		self.customer = frappe.get_last_doc("Customer")
		self.supplier = frappe.get_last_doc("Supplier")
		self.item = frappe.get_last_doc("Item")

		# Get or create customer group and supplier group
		try:
			self.customer_group = frappe.get_last_doc("Customer Group")
		except:
			self.customer_group = frappe.get_doc({
				"doctype": "Customer Group",
				"customer_group_name": "Test Customer Group",
				"parent_customer_group": "All Customer Groups"
			}).insert()

		try:
			self.supplier_group = frappe.get_last_doc("Supplier Group")
		except:
			self.supplier_group = frappe.get_doc({
				"doctype": "Supplier Group",
				"supplier_group_name": "Test Supplier Group"
			}).insert()

	def test_item_query_for_customer(self):
		create_party_specific_item(
			party_type="Customer",
			party=self.customer.name,
			restrict_based_on="Item",
			based_on_value=self.item.name,
		)
		filters = {"is_sales_item": 1, "customer": self.customer.name}
		items = item_query(
			doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
		)
		for item in items:
			self.assertEqual(item[0], self.item.name)

	def test_item_query_for_supplier(self):
		create_party_specific_item(
			party_type="Supplier",
			party=self.supplier.name,
			restrict_based_on="Item Group",
			based_on_value=self.item.item_group,
		)
		filters = {"supplier": self.supplier.name, "is_purchase_item": 1}
		items = item_query(
			doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
		)
		for item in items:
			self.assertEqual(item[2], self.item.item_group)

	def test_item_query_for_customer_exclusive(self):
		# Create an exclusive party specific item
		create_party_specific_item(
			party_type="Customer",
			party=self.customer.name,
			restrict_based_on="Item",
			based_on_value=self.item.name,
			select="Exclusive"
		)
		filters = {"is_sales_item": 1, "customer": self.customer.name}
		items = item_query(
			doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
		)
		# Verify that the excluded item is not in the results
		item_names = [item[0] for item in items]
		self.assertNotIn(self.item.name, item_names)

	def test_item_query_for_supplier_exclusive(self):
		# Create an exclusive party specific item for item group
		create_party_specific_item(
			party_type="Supplier",
			party=self.supplier.name,
			restrict_based_on="Item Group",
			based_on_value=self.item.item_group,
			select="Exclusive"
		)
		filters = {"supplier": self.supplier.name, "is_purchase_item": 1}
		items = item_query(
			doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
		)
		# Verify that items from the excluded item group are not in the results
		for item in items:
			self.assertNotEqual(item[2], self.item.item_group)

	def test_item_query_for_customer_group_inclusive(self):
		# Create a party specific item for customer group with inclusive filter
		create_party_specific_item(
			party_type="Customer Group",
			party=self.customer_group.name,
			restrict_based_on="Item",
			based_on_value=self.item.name,
			select="Inclusive"
		)

		# Update customer to belong to this group
		customer_doc = frappe.get_doc("Customer", self.customer.name)
		customer_doc.customer_group = self.customer_group.name
		customer_doc.save()

		filters = {"is_sales_item": 1, "customer": self.customer.name}
		items = item_query(
			doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
		)
		# Verify that only the included item is in the results
		for item in items:
			self.assertEqual(item[0], self.item.name)

	def test_item_query_for_customer_group_exclusive(self):
		# Create a party specific item for customer group with exclusive filter
		create_party_specific_item(
			party_type="Customer Group",
			party=self.customer_group.name,
			restrict_based_on="Item",
			based_on_value=self.item.name,
			select="Exclusive"
		)

		# Update customer to belong to this group
		customer_doc = frappe.get_doc("Customer", self.customer.name)
		customer_doc.customer_group = self.customer_group.name
		customer_doc.save()

		filters = {"is_sales_item": 1, "customer": self.customer.name}
		items = item_query(
			doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
		)
		# Verify that the excluded item is not in the results
		item_names = [item[0] for item in items]
		self.assertNotIn(self.item.name, item_names)

	def test_item_query_for_supplier_group_inclusive(self):
		# Create a party specific item for supplier group with inclusive filter
		create_party_specific_item(
			party_type="Supplier Group",
			party=self.supplier_group.name,
			restrict_based_on="Item Group",
			based_on_value=self.item.item_group,
			select="Inclusive"
		)

		# Update supplier to belong to this group
		supplier_doc = frappe.get_doc("Supplier", self.supplier.name)
		supplier_doc.supplier_group = self.supplier_group.name
		supplier_doc.save()

		filters = {"supplier": self.supplier.name, "is_purchase_item": 1}
		items = item_query(
			doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
		)
		# Verify that only items from the included item group are in the results
		for item in items:
			self.assertEqual(item[2], self.item.item_group)

	def test_item_query_for_supplier_group_exclusive(self):
		# Create a party specific item for supplier group with exclusive filter
		create_party_specific_item(
			party_type="Supplier Group",
			party=self.supplier_group.name,
			restrict_based_on="Item Group",
			based_on_value=self.item.item_group,
			select="Exclusive"
		)

		# Update supplier to belong to this group
		supplier_doc = frappe.get_doc("Supplier", self.supplier.name)
		supplier_doc.supplier_group = self.supplier_group.name
		supplier_doc.save()

		filters = {"supplier": self.supplier.name, "is_purchase_item": 1}
		items = item_query(
			doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
		)
		# Verify that items from the excluded item group are not in the results
		for item in items:
			self.assertNotEqual(item[2], self.item.item_group)
