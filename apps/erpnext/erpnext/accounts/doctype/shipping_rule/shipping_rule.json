{"allow_import": 1, "autoname": "field:label", "creation": "2013-06-25 11:48:03", "description": "Specify conditions to calculate shipping amount", "doctype": "DocType", "field_order": ["label", "disabled", "column_break_4", "shipping_rule_type", "section_break_10", "company", "column_break_12", "account", "accounting_dimensions_section", "cost_center", "dimension_col_break", "project", "shipping_amount_section", "calculate_based_on", "column_break_8", "shipping_amount", "rule_conditions_section", "conditions", "section_break_6", "countries"], "fields": [{"description": "example: Next Day Shipping", "fieldname": "label", "fieldtype": "Data", "label": "Shipping Rule Label", "reqd": 1, "unique": 1}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "shipping_rule_type", "fieldtype": "Select", "label": "Shipping Rule Type", "options": "Selling\nBuying"}, {"depends_on": "eval:!doc.disabled", "fieldname": "section_break_10", "fieldtype": "Section Break", "label": "Accounting"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "remember_last_selected_value": 1, "reqd": 1}, {"fieldname": "column_break_12", "fieldtype": "Column Break"}, {"fieldname": "account", "fieldtype": "Link", "label": "Shipping Account", "options": "Account", "reqd": 1}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center", "reqd": 1}, {"fieldname": "shipping_amount_section", "fieldtype": "Section Break"}, {"default": "Fixed", "fieldname": "calculate_based_on", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Calculate Based On", "options": "Fixed\nNet Total\nNet Weight"}, {"fieldname": "column_break_8", "fieldtype": "Column Break"}, {"depends_on": "eval:doc.calculate_based_on==='Fixed'", "fieldname": "shipping_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Shipping Amount"}, {"depends_on": "eval:doc.calculate_based_on!=='Fixed'", "fieldname": "rule_conditions_section", "fieldtype": "Section Break", "label": "Shipping Rule Conditions"}, {"fieldname": "conditions", "fieldtype": "Table", "label": "Shipping Rule Conditions", "options": "Shipping Rule Condition"}, {"fieldname": "section_break_6", "fieldtype": "Section Break", "label": "Restrict to Countries"}, {"fieldname": "countries", "fieldtype": "Table", "label": "Valid for Countries", "options": "Shipping Rule Country"}, {"fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Project"}], "icon": "fa fa-truck", "idx": 1, "modified": "2019-05-25 23:12:26.156405", "modified_by": "Administrator", "module": "Accounts", "name": "Shipping Rule", "owner": "Administrator", "permissions": [{"email": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User"}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User"}, {"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "set_user_permissions": 1, "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Master Manager", "set_user_permissions": 1, "share": 1, "write": 1}], "sort_order": "ASC"}