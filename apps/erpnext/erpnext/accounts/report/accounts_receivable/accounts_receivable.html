<style>
	.print-format {
		padding: 4mm;
		font-size: 8.0pt !important;
	}
	.print-format td {
		vertical-align:middle !important;
	}
	</style>

	<h2 class="text-center" style="margin-top:0">{%= __(report.report_name) %}</h2>
	<h4 class="text-center">
		{% if (filters.party) { %}
			{%= __(filters.party) %}
		{% } %}
	</h4>
	<h6 class="text-center">
			{% if (filters.tax_id) { %}
			{%= __("Tax Id: ")%}	{%= filters.tax_id %}
			{% } %}
	</h6>
	<h5 class="text-center">
		{%= __(filters.ageing_based_on) %}
		{%= __("Until") %}
		{%= frappe.datetime.str_to_user(filters.report_date) %}
	</h5>

	<div class="clearfix">
		<div class="pull-left">
		{% if(filters.payment_terms) { %}
			<strong>{%= __("Payment Terms") %}:</strong> {%= filters.payment_terms %}
		{% } %}
		</div>
		<div class="pull-right">
		{% if(filters.credit_limit) { %}
			<strong>{%= __("Credit Limit") %}:</strong> {%= format_currency(filters.credit_limit) %}
		{% } %}
		</div>
	</div>

	{% if(filters.show_future_payments) { %}
		{% var balance_row = data.slice(-1).pop();
			var start = report.columns.findIndex((elem) => (elem.fieldname == 'age'));
			var range1 = report.columns[start].label;
			var range2 = report.columns[start+1].label;
			var range3 = report.columns[start+2].label;
			var range4 = report.columns[start+3].label;
			var range5 = report.columns[start+4].label;
			var range6 = report.columns[start+5].label;
		%}
		{% if(balance_row) { %}
		<table class="table table-bordered table-condensed">
			<caption class="text-right">(Amount in {%= data[0]["currency"] || "" %})</caption>
				<colgroup>
					<col style="width: 30mm;">
					<col style="width: 18mm;">
					<col style="width: 18mm;">
					<col style="width: 18mm;">
					<col style="width: 18mm;">
					<col style="width: 18mm;">
					<col style="width: 18mm;">
					<col style="width: 18mm;">
				</colgroup>

			<thead>
				<tr>
					<th>{%= __(" ") %}</th>
					<th>{%= __(range1) %}</th>
					<th>{%= __(range2) %}</th>
					<th>{%= __(range3) %}</th>
					<th>{%= __(range4) %}</th>
					<th>{%= __(range5) %}</th>
					<th>{%= __(range6) %}</th>
					<th>{%= __("Total") %}</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td>{%= __("Total Outstanding") %}</td>
					<td class="text-right">
						{%= format_number(balance_row["age"], null, 2) %}
					</td>
					<td class="text-right">
						{%= format_currency(balance_row["range1"], data[data.length-1]["currency"]) %}
					</td>
					<td class="text-right">
						{%= format_currency(balance_row["range2"], data[data.length-1]["currency"]) %}
					</td>
					<td class="text-right">
						{%= format_currency(balance_row["range3"], data[data.length-1]["currency"]) %}
					</td>
					<td class="text-right">
						{%= format_currency(balance_row["range4"], data[data.length-1]["currency"]) %}
					</td>
					<td class="text-right">
						{%= format_currency(balance_row["range5"], data[data.length-1]["currency"]) %}
					</td>
					<td class="text-right">
						{%= format_currency(flt(balance_row["outstanding"]), data[data.length-1]["currency"]) %}
					</td>
				</tr>
					<td>{%= __("Future Payments") %}</td>
					<td></td>
					<td></td>
					<td></td>
					<td></td>
					<td></td>
					<td></td>
					<td class="text-right">
						{%= format_currency(flt(balance_row[("future_amount")]), data[data.length-1]["currency"]) %}
					</td>
				<tr class="cvs-footer">
					<th class="text-left">{%= __("Cheques Required") %}</th>
					<th></th>
					<th></th>
					<th></th>
					<th></th>
					<th></th>
					<th></th>
					<th class="text-right">
						{%= format_currency(flt(balance_row["outstanding"] - balance_row[("future_amount")]), data[data.length-1]["currency"]) %}</th>
				</tr>
			</tbody>

		</table>
		{% } %}
	{% } %}
	<table class="table table-bordered">
		<thead>
			<tr>
				{% if(report.report_name === "Accounts Receivable" || report.report_name === "Accounts Payable") { %}
					<th style="width: 10%">{%= __("Date") %}</th>
					<th style="width: 4%">{%= __("Age (Days)") %}</th>

					{% if(report.report_name === "Accounts Receivable" && filters.show_sales_person) { %}
						<th style="width: 14%">{%= __("Reference") %}</th>
						<th style="width: 10%">{%= __("Sales Person") %}</th>
					{% } else { %}
						<th style="width: 24%">{%= __("Reference") %}</th>
					{% } %}
					{% if(!filters.show_future_payments) { %}
						<th style="width: 20%">{%= (filters.party) ? __("Remarks"): __("Party") %}</th>
					{% } %}
					<th style="width: 10%; text-align: right">{%= __("Invoiced Amount") %}</th>
					{% if(!filters.show_future_payments) { %}
						<th style="width: 10%; text-align: right">{%= __("Paid Amount") %}</th>
						<th style="width: 10%; text-align: right">{%= report.report_name === "Accounts Receivable" ? __('Credit Note') : __('Debit Note') %}</th>
					{% } %}
					<th style="width: 10%; text-align: right">{%= __("Outstanding Amount") %}</th>
					{% if(filters.show_future_payments) { %}
						{% if(report.report_name === "Accounts Receivable") { %}
							<th style="width: 12%">{%= __("Customer LPO No.") %}</th>
						{% } %}
						<th style="width: 10%">{%= __("Future Payment Ref") %}</th>
						<th style="width: 10%">{%= __("Future Payment Amount") %}</th>
						<th style="width: 10%">{%= __("Remaining Balance") %}</th>
					{% } %}
				{% } else { %}
					<th style="width: 40%">{%= (filters.party) ? __("Remarks"): __("Party") %}</th>
					<th style="width: 15%">{%= __("Total Invoiced Amount") %}</th>
					<th style="width: 15%">{%= __("Total Paid Amount") %}</th>
					<th style="width: 15%">{%= report.report_name === "Accounts Receivable Summary" ? __('Credit Note Amount') : __('Debit Note Amount') %}</th>
					<th style="width: 15%">{%= __("Total Outstanding Amount") %}</th>
				{% } %}
			</tr>
		</thead>
		<div class="show-filters">
			{% if subtitle %}
				{{ subtitle }}
				<hr>
			{% endif %}
		</div>
		<tbody>
			{% for(var i=0, l=data.length; i<l; i++) { %}
				<tr>
				{% if(report.report_name === "Accounts Receivable" || report.report_name === "Accounts Payable") { %}
					{% if(data[i]["party"]) { %}
						<td>{%= frappe.datetime.str_to_user(data[i]["posting_date"]) %}</td>
						<td style="text-align: right">{%= data[i]["age"] %}</td>
						<td>
							{% if(!filters.show_future_payments) { %}
								{%= data[i]["voucher_type"] %}
								<br>
							{% } %}
							{%= data[i]["voucher_no"] %}
						</td>

						{% if(report.report_name === "Accounts Receivable" && filters.show_sales_person) { %}
						<td>{%= data[i]["sales_person"] %}</td>
						{% } %}

						{% if(!filters.show_future_payments) { %}
						<td>
							{% if(!filters.party?.length) { %}
								{%= data[i]["party"] %}
								{% if(data[i]["customer_name"] && data[i]["customer_name"] != data[i]["party"]) { %}
									<br> {%= data[i]["customer_name"] %}
								{% } else if(data[i]["supplier_name"] != data[i]["party"]) { %}
									<br> {%= data[i]["supplier_name"] %}
								{% } %}
							{% } %}
							<div>
							{% if data[i]["remarks"] %}
								{%= __("Remarks") %}:
								{%= data[i]["remarks"] %}
							{% } %}
							</div>
						</td>
						{% } %}

						<td style="text-align: right">
							{%= format_currency(data[i]["invoiced"], data[i]["currency"]) %}</td>

						{% if(!filters.show_future_payments) { %}
							<td style="text-align: right">
								{%= format_currency(data[i]["paid"], data[i]["currency"]) %}</td>
							<td style="text-align: right">
								{%= format_currency(data[i]["credit_note"], data[i]["currency"]) %}</td>
						{% } %}
						<td style="text-align: right">
							{%= format_currency(data[i]["outstanding"], data[i]["currency"]) %}</td>

						{% if(filters.show_future_payments) { %}
							{% if(report.report_name === "Accounts Receivable") { %}
								<td style="text-align: right">
									{%= data[i]["po_no"] %}</td>
							{% } %}
							<td style="text-align: right">{%= data[i]["future_ref"] %}</td>
							<td style="text-align: right">{%= format_currency(data[i]["future_amount"], data[i]["currency"]) %}</td>
							<td style="text-align: right">{%= format_currency(data[i]["remaining_balance"], data[i]["currency"]) %}</td>
						{% } %}
					{% } else { %}
						<td></td>
						{% if(!filters.show_future_payments) { %}
						<td></td>
						{% } %}
						{% if(report.report_name === "Accounts Receivable" && filters.show_sales_person) { %}
						<td></td>
						{% } %}
						<td></td>
						<td style="text-align: right"><b>{%= __("Total") %}</b></td>
						<td style="text-align: right">
							{%= format_currency(data[i]["invoiced"], data[i]["currency"] ) %}</td>

						{% if(!filters.show_future_payments) { %}
							<td style="text-align: right">
								{%= format_currency(data[i]["paid"], data[i]["currency"]) %}</td>
							<td style="text-align: right">{%= format_currency(data[i]["credit_note"], data[i]["currency"]) %} </td>
						{% } %}
						<td style="text-align: right">
							{%= format_currency(data[i]["outstanding"], data[i]["currency"]) %}</td>

						{% if(filters.show_future_payments) { %}
							{% if(report.report_name === "Accounts Receivable") { %}
								<td style="text-align: right">
									{%= data[i]["po_no"] %}</td>
							{% } %}
							<td style="text-align: right">{%= data[i]["future_ref"] %}</td>
							<td style="text-align: right">{%= format_currency(data[i]["future_amount"], data[i]["currency"]) %}</td>
							<td style="text-align: right">{%= format_currency(data[i]["remaining_balance"], data[i]["currency"]) %}</td>
						{% } %}
					{% } %}
				{% } else { %}
					{% if(data[i]["party"]|| "&nbsp;") { %}
						{% if(!data[i]["is_total_row"]) { %}
							<td>
								{% if(!filters.party?.length) { %}
									{%= data[i]["party"] %}
									{% if(data[i]["customer_name"] && data[i]["customer_name"] != data[i]["party"]) { %}
										<br> {%= data[i]["customer_name"] %}
									{% } else if(data[i]["supplier_name"] != data[i]["party"]) { %}
										<br> {%= data[i]["supplier_name"] %}
									{% } %}
								{% } %}
								<br>{%= __("Remarks") %}:
								{%= data[i]["remarks"] %}
							</td>
						{% } else { %}
							<td><b>{%= __("Total") %}</b></td>
						{% } %}
						<td style="text-align: right">{%= format_currency(data[i]["invoiced"], data[i]["currency"]) %}</td>
						<td style="text-align: right">{%= format_currency(data[i]["paid"], data[i]["currency"]) %}</td>
						<td style="text-align: right">{%= format_currency(data[i]["credit_note"], data[i]["currency"]) %}</td>
						<td style="text-align: right">{%= format_currency(data[i]["outstanding"], data[i]["currency"]) %}</td>
					{% } %}
				{% } %}
				</tr>
			{% } %}
		</tbody>
	</table>
	<p class="text-right text-muted">{%= __("Printed on {0}", [frappe.datetime.str_to_user(frappe.datetime.get_datetime_as_string())]) %}</p>
