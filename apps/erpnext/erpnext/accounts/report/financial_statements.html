{%
	var report_columns = report.get_columns_for_print();
	report_columns = report_columns.filter(col => !col.hidden);

	if (report_columns.length > 8) {
		frappe.throw(__("Too many columns. Export the report and print it using a spreadsheet application."));
	}
%}

<style>
	.financial-statements-important td {
		font-weight: bold;
	}

	.financial-statements-blank-row td {
		height: 37px;
	}
</style>

<h2 class="text-center">{%= __(report.report_name) %}</h2>
<h3 class="text-center">{%= filters.company %}</h3>

{% if 'cost_center' in filters %}
	<h3 class="text-center">{%= filters.cost_center %}</h3>
{% endif %}

<h3 class="text-center">{%= filters.fiscal_year %}</h3>
<h5 class="text-center">
	{%=  __("Currency") %} : {%= filters.presentation_currency || erpnext.get_currency(filters.company) %}
</h5>
{% if (filters.from_date) { %}
	<h5 class="text-center">
		{%= frappe.datetime.str_to_user(filters.from_date) %} - {%= frappe.datetime.str_to_user(filters.to_date) %}
	</h5>
{% } %}
<hr>
<div class="show-filters">
			{% if subtitle %}
				{{ subtitle }}
				<hr>
			{% endif %}
</div>
<table class="table table-bordered">
	<thead>
		<tr>
			<th style="width: {%= 100 - (report_columns.length - 1) * 13 %}%"></th>
			{% for (let i=1, l=report_columns.length; i<l; i++) { %}
				<th class="text-right">{%= report_columns[i].label %}</th>
			{% } %}
		</tr>
	</thead>
	<tbody>
		{% for(let j=0, k=data.length; j<k; j++) { %}
			{%
				var row = data[j];
				var row_class = data[j].parent_account || data[j].parent_section ? "" : "financial-statements-important";
				row_class += data[j].account_name || data[j].section ? "" : " financial-statements-blank-row";
			%}
			<tr class="{%= row_class %}">
				<td>
					<span style="padding-left: {%= cint(data[j].indent) * 2 %}em">{%= row.account_name || row.section %}</span>
				</td>
				{% for(let i=1, l=report_columns.length; i<l; i++) { %}
					<td class="text-right">
						{% const fieldname = report_columns[i].fieldname; %}
						{% if (!is_null(row[fieldname])) { %}
							{%= frappe.format(row[fieldname], report_columns[i], {}, row) %}
						{% } %}
					</td>
				{% } %}
			</tr>
		{% } %}
	</tbody>
</table>
<p class="text-right text-muted">
	{%= __("Printed on {0}", [frappe.datetime.str_to_user(frappe.datetime.get_datetime_as_string())]) %}
</p>
