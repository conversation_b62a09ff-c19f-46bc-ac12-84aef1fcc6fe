["tabDocField", "tabTag Link", "tabAdvance", "tabDocType", "tabJournal", "tabBank", "tabSalary", "tabPayment Entry", "tabPrint", "tabBOM Creator Item", "tabContainer", "tabPatch Log", "tabSales", "tabSales Invoice Item", "tabAppraisal", "tabPatch", "tabPurchase", "tabPayment", "tabShipping", "tabPick", "tabPick List Item", "tabJournal Entry Account", "tabVehicle Log", "tabDeleted Document", "tabPayment Entry Reference", "tabPortal", "tabBOM", "tabTag", "tabCustom", "tabBank Guarantee", "tabAsset Capitalization", "tabRoute", "tabScheduled Job Type", "tabDocType State", "tabDeleted", "tabAdvance Payment Ledger Entry", "tabScheduled", "tabSingles", "tabLoyalty", "tabJournal Entry", "tabBank Transaction", "tabShipping Rule", "tabNumber", "tabDocType Link", "tabComment", "tabDocType Action", "tabAsset Capitalization Asset Item", "tabAppraisal Goal", "tabEmployee", "tabNumber Card", "tabPortal Menu Item", "tabSubcontracting Receipt Item", "tabVehicle", "tabProperty", "tabEmployee OT Component", "tabRoute History", "tabInstalled", "tabProperty Setter", "tabInstalled Application", "tabLoyalty Program", "tabDocPerm", "tabSalary Slip OT Component", "tabAsset", "tabCustom Field", "tabPrint Heading", "tabSubcontracting", "tabVehicle Service"]