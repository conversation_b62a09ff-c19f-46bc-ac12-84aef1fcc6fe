2025-09-04 17:43:19,814 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `no_of_hours` decimal(21,9) not null default 0, <PERSON>OD<PERSON><PERSON> `auto_repeat_end_date` date, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0
2025-09-04 17:43:20,066 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-09-04 17:43:20,261 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration` MODIFY `total_exemption_amount` decimal(21,9) not null default 0, MODIFY `total_declared_amount` decimal(21,9) not null default 0
2025-09-04 17:43:20,418 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` MODIFY `to_amount` decimal(21,9) not null default 0
2025-09-04 17:43:20,615 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` MODIFY `working_hours` decimal(21,9) not null default 0
2025-09-04 17:43:20,753 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` MODIFY `min_taxable_income` decimal(21,9) not null default 0, MODIFY `max_taxable_income` decimal(21,9) not null default 0, MODIFY `percent` decimal(21,9) not null default 0
2025-09-04 17:43:21,012 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` MODIFY `used_leaves` decimal(21,9) not null default 0, MODIFY `total_allocated_leaves` decimal(21,9) not null default 0, MODIFY `pending_leaves` decimal(21,9) not null default 0, MODIFY `available_leaves` decimal(21,9) not null default 0, MODIFY `expired_leaves` decimal(21,9) not null default 0
2025-09-04 17:43:21,216 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application` MODIFY `remaining_benefit` decimal(21,9) not null default 0, MODIFY `pro_rata_dispensed_amount` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `max_benefits` decimal(21,9) not null default 0
2025-09-04 17:43:21,410 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Sub Category` MODIFY `max_amount` decimal(21,9) not null default 0
2025-09-04 17:43:21,562 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule Slab` MODIFY `fraction_of_applicable_earnings` decimal(21,9) not null default 0
2025-09-04 17:43:21,729 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure` MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `leave_encashment_amount_per_day` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `total_earning` decimal(21,9) not null default 0, MODIFY `max_benefits` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0
2025-09-04 17:43:22,191 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Statement Summary` ADD COLUMN `closing_balance` decimal(21,9) not null default 0
2025-09-04 17:43:22,217 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Statement Summary` MODIFY `withdrawal` decimal(21,9) not null default 0, MODIFY `deposit` decimal(21,9) not null default 0
2025-09-04 17:43:22,506 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Piecework Additional Salary` MODIFY `amount` decimal(21,9) not null default 0
2025-09-04 17:43:22,723 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-04 17:43:22,917 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA TAX Inv` ADD COLUMN `customer_name` varchar(140), ADD COLUMN `customer_id_type` varchar(140), ADD COLUMN `customer_id` varchar(140), ADD COLUMN `customer_mobile` varchar(140), ADD COLUMN `reference_doctype` varchar(140), ADD COLUMN `reference_docname` varchar(140)
2025-09-04 17:43:22,945 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA TAX Inv` MODIFY `subtotal` decimal(21,9) not null default 0, MODIFY `total_tax` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-09-04 17:43:23,363 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Fine Record` MODIFY `charge` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `penalty` decimal(21,9) not null default 0
2025-09-04 17:43:23,598 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill` MODIFY `billedamount` decimal(21,9) not null default 0, MODIFY `billequivalentamount` decimal(21,9) not null default 0, MODIFY `miscellaneousamount` decimal(21,9) not null default 0
2025-09-04 17:43:23,772 WARNING database DDL Query made to DB:
ALTER TABLE `tabSingle Piecework Employees` MODIFY `task_rate` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-09-04 17:43:23,918 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Salary Component Limit` MODIFY `limit_amount` decimal(21,9) not null default 0
2025-09-04 17:43:24,047 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-04 17:43:24,189 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA TAX Inv Item` ADD COLUMN `mapped_item_code` varchar(140)
2025-09-04 17:43:24,219 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA TAX Inv Item` MODIFY `amount` decimal(21,9) not null default 0
2025-09-04 17:43:25,148 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-09-04 17:43:25,283 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `net_weight_index`, DROP INDEX `gross_weight_index`, DROP INDEX `container_no_index`
2025-09-04 17:43:25,673 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-09-04 17:43:26,079 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-04 17:43:26,630 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-04 17:43:26,798 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-04 17:43:27,121 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_volume` decimal(21,9) not null default 0
2025-09-04 17:43:27,384 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-09-04 17:43:28,108 WARNING database DDL Query made to DB:
truncate `tabAdvance Payment Ledger Entry`
2025-09-04 17:47:13,179 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0
2025-09-05 11:41:10,860 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-05 11:41:13,949 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-05 11:41:17,364 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-05 11:41:20,719 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-09-05 11:41:21,503 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-05 11:41:22,917 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-05 11:41:23,743 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-05 11:41:25,846 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-09-05 11:41:25,933 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `net_weight_index`, DROP INDEX `container_no_index`, DROP INDEX `gross_weight_index`
2025-09-05 11:41:26,808 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-09-05 11:41:27,394 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-05 11:41:28,970 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-05 11:41:29,406 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-05 11:41:30,112 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_volume` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-09-05 11:41:30,284 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-09-05 11:41:41,562 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0
2025-09-08 11:25:41,348 WARNING database DDL Query made to DB:
ALTER TABLE `tabGate Pass` ADD COLUMN `custom_expiry_date` date
2025-09-08 11:25:41,493 WARNING database DDL Query made to DB:
ALTER TABLE `tabGate Pass` ADD COLUMN `custom_expiry_time` time(6)
2025-09-08 11:27:19,315 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-08 11:27:20,496 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-08 11:27:21,875 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-08 11:27:23,194 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-09-08 11:27:23,515 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-09-08 11:27:24,085 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-08 11:27:24,420 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-08 11:27:25,105 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-09-08 11:27:25,155 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `container_no_index`, DROP INDEX `net_weight_index`, DROP INDEX `gross_weight_index`
2025-09-08 11:27:25,520 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-09-08 11:27:25,726 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-08 11:27:26,225 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-08 11:27:26,392 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-08 11:27:26,731 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_volume` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-09-08 11:27:26,964 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-09-08 11:27:32,019 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-09-08 11:28:45,244 WARNING database DDL Query made to DB:
ALTER TABLE `tabGate Pass` ADD COLUMN `custom_icd` varchar(140)
2025-09-08 11:45:29,180 WARNING database DDL Query made to DB:
ALTER TABLE `tabGate Pass` ADD COLUMN `expiry_date` date, ADD COLUMN `expiry_time` time(6)
2025-09-10 12:15:10,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabGate Pass` MODIFY `expiry_date` datetime(6)
2025-09-10 17:48:26,926 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-10 17:48:29,130 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-10 17:48:32,326 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-10 17:48:34,743 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-09-10 17:48:35,403 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-10 17:48:36,527 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-10 17:48:37,224 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-10 17:48:38,591 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-09-10 17:48:38,638 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `net_weight_index`, DROP INDEX `container_no_index`, DROP INDEX `gross_weight_index`
2025-09-10 17:48:39,283 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-09-10 17:48:39,644 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-09-10 17:48:40,634 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-10 17:48:40,959 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-10 17:48:41,395 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `gross_volume` decimal(21,9) not null default 0
2025-09-10 17:48:41,535 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-09-10 17:48:51,425 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0
2025-09-11 10:08:19,810 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-11 10:08:20,931 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Format` ADD COLUMN `print_format_for` varchar(140) default 'DocType', ADD COLUMN `report` varchar(140)
2025-09-11 10:08:21,324 WARNING database DDL Query made to DB:
create table `tabUser Role` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-09-11 10:08:21,783 WARNING database DDL Query made to DB:
create table `tabUser Invitation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email` varchar(140),
`app_name` varchar(140),
`redirect_to_path` varchar(140),
`status` varchar(140) default 'Pending',
`invited_by` varchar(140),
`key` varchar(140),
`user` varchar(140),
`email_sent_at` datetime(6),
`accepted_at` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-09-11 10:08:22,325 WARNING database DDL Query made to DB:
ALTER TABLE `tabEvent` ADD COLUMN `reference_doctype` varchar(140), ADD COLUMN `reference_docname` varchar(140)
2025-09-11 10:08:22,687 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-11 10:08:23,690 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule` MODIFY `recurse_for` decimal(21,9) not null default 0, MODIFY `free_item_rate` decimal(21,9) not null default 0, MODIFY `min_qty` decimal(21,9) not null default 0, MODIFY `threshold_percentage` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `max_qty` decimal(21,9) not null default 0
2025-09-11 10:08:24,786 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation Item` MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `blanket_order_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `gross_profit` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-09-11 10:08:25,049 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-11 10:08:25,417 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-09-11 10:08:26,607 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-09-11 10:08:26,910 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-11 10:08:27,311 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0
2025-09-11 10:08:27,811 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-11 10:08:28,144 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-11 10:08:28,867 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-09-11 10:08:28,930 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `gross_weight_index`, DROP INDEX `container_no_index`, DROP INDEX `net_weight_index`
2025-09-11 10:08:29,272 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-09-11 10:08:29,536 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-09-11 10:08:30,102 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-11 10:08:30,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-11 10:08:30,549 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_volume` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-09-11 10:08:30,713 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-09-11 10:08:31,192 WARNING database DDL Query made to DB:
ALTER TABLE `tabScheduler Event` ADD INDEX `creation`(`creation`)
2025-09-11 10:08:41,266 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0
2025-09-22 08:42:45,887 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-22 08:43:12,598 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-26 08:52:59,602 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-26 08:53:01,710 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-26 08:53:02,977 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0
2025-09-26 08:53:03,805 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-26 08:53:04,439 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial and Batch Bundle` MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `avg_rate` decimal(21,9) not null default 0
2025-09-26 08:53:04,471 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial and Batch Bundle` ADD INDEX `voucher_type_index`(`voucher_type`)
2025-09-26 08:53:04,502 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial and Batch Bundle` DROP INDEX `type_of_transaction`
2025-09-26 08:53:05,529 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-09-26 08:53:05,869 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-26 08:53:06,454 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-26 08:53:06,604 WARNING database DDL Query made to DB:
create sequence if not exists vehicle_sync_task_id_seq nocache nocycle
2025-09-26 08:53:06,652 WARNING database DDL Query made to DB:
create table `tabVehicle Sync Task` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`vehicle_no` varchar(140) unique,
`priority` int(11) not null default 0,
`status` varchar(140),
`attempts` int(11) not null default 0,
`backoff_exp` int(11) not null default 0,
`next_run_at` datetime(6),
`last_run_at` datetime(6),
`claimed_at` datetime(6),
`claimed_by` varchar(140),
`last_error` text,
`is_deleted` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `priority`(`priority`),
index `status`(`status`),
index `next_run_at`(`next_run_at`),
index `last_run_at`(`last_run_at`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-09-26 08:53:06,876 WARNING database DDL Query made to DB:
create table `tabVehicle Inspection Record` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`vir_no` varchar(140) unique,
`inspection_id` int(11) not null default 0,
`vid` int(11) not null default 0,
`noplate` varchar(140),
`licence` varchar(140),
`vehicle_doc` varchar(140),
`inspection_date` date,
`valid_until` date,
`final_result` varchar(140),
`inspector` varchar(140),
`inspector_id` varchar(140),
`email` varchar(140),
`region` varchar(140),
`district` varchar(140),
`driver_name` varchar(140),
`driver_address` text,
`vehicle_passed_for` varchar(140),
`weight` varchar(140),
`prohibition_on_use` varchar(140),
`originates` varchar(140),
`speed_test` varchar(140),
`electrical_system` varchar(140),
`fitting_equipment` varchar(140),
`braking_system` varchar(140),
`wheels` varchar(140),
`suspension` varchar(140),
`steering` varchar(140),
`engine` varchar(140),
`exhaust` varchar(140),
`transmission` varchar(140),
`instruments_panel` varchar(140),
`dimensions` varchar(140),
`radiation` varchar(140),
`remarks` longtext,
`created_at` datetime(6),
`updated_at` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-09-26 08:53:07,336 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-26 08:53:08,075 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-09-26 08:53:08,114 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `gross_weight_index`, DROP INDEX `container_no_index`, DROP INDEX `net_weight_index`
2025-09-26 08:53:08,458 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-09-26 08:53:08,674 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-26 08:53:09,288 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-26 08:53:09,459 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-26 08:53:09,998 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_volume` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-09-26 08:53:10,152 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-09-26 08:53:16,046 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0
2025-09-26 09:00:29,682 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-26 09:00:30,520 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-26 09:00:31,628 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-26 09:00:32,682 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-09-26 09:00:33,013 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-26 09:00:33,612 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-26 09:00:33,925 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-26 09:00:34,519 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-09-26 09:00:34,586 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `net_weight_index`, DROP INDEX `container_no_index`, DROP INDEX `gross_weight_index`
2025-09-26 09:00:34,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-09-26 09:00:35,137 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-26 09:00:35,602 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-26 09:00:35,740 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-26 09:00:36,071 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_volume` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-09-26 09:00:36,271 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-09-26 09:00:40,557 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0
2025-09-29 16:19:50,089 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-29 16:19:51,403 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-29 16:19:53,036 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-29 16:19:54,517 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-09-29 16:19:54,940 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-29 16:19:55,740 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-29 16:19:56,108 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-29 16:19:56,885 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-09-29 16:19:56,924 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `gross_weight_index`, DROP INDEX `net_weight_index`, DROP INDEX `container_no_index`
2025-09-29 16:19:57,391 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-09-29 16:19:57,623 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-29 16:19:58,197 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-29 16:19:58,377 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-29 16:19:58,835 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `gross_volume` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-09-29 16:19:58,966 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-09-29 16:20:06,189 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0
2025-09-29 16:50:33,585 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-29 16:50:34,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-29 16:50:35,900 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-29 16:50:37,142 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-09-29 16:50:37,422 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-29 16:50:37,920 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-29 16:50:38,282 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-29 16:50:38,896 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-09-29 16:50:38,956 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `net_weight_index`, DROP INDEX `container_no_index`, DROP INDEX `gross_weight_index`
2025-09-29 16:50:39,239 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-09-29 16:50:39,436 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-29 16:50:39,949 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-29 16:50:40,095 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-09-29 16:50:40,431 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `gross_volume` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-09-29 16:50:40,601 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-09-29 16:50:45,410 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0
2025-10-03 09:07:24,820 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD COLUMN `custom_include_in_payroll_summary` int(1) not null default 0
2025-10-03 09:07:24,849 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0
2025-10-03 09:07:24,998 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0
2025-10-03 09:08:35,665 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0
2025-10-03 09:13:00,446 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component`
				CHANGE COLUMN `custom_include_in_payroll_summary`
				`include_in_payroll_summary`
				int(1)
2025-10-03 14:58:52,357 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-03 14:58:53,274 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-03 14:58:54,334 WARNING database DDL Query made to DB:
ALTER TABLE `tabParty Specific Item` ADD COLUMN `select` varchar(140) default 'Inclusive'
2025-10-03 14:58:54,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-03 14:58:55,682 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-03 14:58:55,959 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-10-03 14:58:56,445 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-10-03 14:58:56,751 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-10-03 14:58:57,338 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-10-03 14:58:57,379 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `net_weight_index`, DROP INDEX `container_no_index`, DROP INDEX `gross_weight_index`
2025-10-03 14:58:57,653 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-03 14:58:57,838 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-03 14:58:58,322 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-10-03 14:58:58,471 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-10-03 14:58:58,753 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `gross_volume` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-10-03 14:58:58,933 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-10-03 14:59:04,870 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0
2025-10-06 08:42:31,293 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-06 08:42:32,384 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-06 08:42:33,773 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-06 08:42:34,951 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-06 08:42:35,290 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-10-06 08:42:35,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-10-06 08:42:36,134 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-10-06 08:42:36,768 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-10-06 08:42:36,814 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `net_weight_index`, DROP INDEX `container_no_index`, DROP INDEX `gross_weight_index`
2025-10-06 08:42:37,104 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-06 08:42:37,306 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-10-06 08:42:37,793 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-10-06 08:42:37,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-10-06 08:42:38,246 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_volume` decimal(21,9) not null default 0
2025-10-06 08:42:38,401 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-10-06 08:42:43,054 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0
2025-10-06 10:22:57,565 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-06 10:22:58,466 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocField` MODIFY `link_filters` json
2025-10-06 10:22:58,905 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` MODIFY `rows_threshold_for_grid_search` int(11) not null default 20
2025-10-06 10:23:00,035 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-06 10:23:00,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Taxes and Charges` ADD COLUMN `project` varchar(140)
2025-10-06 10:23:00,623 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Taxes and Charges` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_tax_amount` decimal(21,9) not null default 0, MODIFY `tax_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0
2025-10-06 10:23:01,048 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Rule` ADD COLUMN `project` varchar(140)
2025-10-06 10:23:01,069 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Rule` MODIFY `shipping_amount` decimal(21,9) not null default 0
2025-10-06 10:23:02,064 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Taxes and Charges` ADD COLUMN `project` varchar(140)
2025-10-06 10:23:02,085 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Taxes and Charges` MODIFY `tax_amount_after_discount_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_tax_amount_after_discount_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_tax_amount` decimal(21,9) not null default 0, MODIFY `tax_amount` decimal(21,9) not null default 0
2025-10-06 10:23:02,327 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoyalty Program` ADD COLUMN `project` varchar(140)
2025-10-06 10:23:02,349 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoyalty Program` MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-10-06 10:23:02,616 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction` MODIFY `deposit` decimal(21,9) not null default 0, MODIFY `withdrawal` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `reference_number` text
2025-10-06 10:23:02,856 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Guarantee` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `margin_money` decimal(21,9) not null default 0, MODIFY `charges` decimal(21,9) not null default 0
2025-10-06 10:23:03,508 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Taxes and Charges` ADD COLUMN `project` varchar(140)
2025-10-06 10:23:03,531 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Taxes and Charges` MODIFY `tax_amount` decimal(21,9) not null default 0, MODIFY `base_tax_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_tax_amount_after_discount_amount` decimal(21,9) not null default 0, MODIFY `tax_amount_after_discount_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-10-06 10:23:04,262 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-06 10:23:04,653 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Creator Item` MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-10-06 10:23:05,113 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` ADD COLUMN `actual_qty` decimal(21,9) not null default 0, ADD COLUMN `company_total_stock` decimal(21,9) not null default 0
2025-10-06 10:23:05,135 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-10-06 10:23:05,667 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization` ADD COLUMN `project` varchar(140)
2025-10-06 10:23:05,691 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization` MODIFY `stock_items_total` decimal(21,9) not null default 0, MODIFY `service_items_total` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `asset_items_total` decimal(21,9) not null default 0, MODIFY `target_incoming_rate` decimal(21,9) not null default 0
2025-10-06 10:23:05,927 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Asset Item` ADD COLUMN `project` varchar(140)
2025-10-06 10:23:05,957 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Asset Item` MODIFY `current_asset_value` decimal(21,9) not null default 0
2025-10-06 10:23:06,587 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Item` ADD COLUMN `service_expense_account` varchar(140)
2025-10-06 10:23:06,609 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Item` MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-10-06 10:23:07,045 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-06 10:23:07,318 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` MODIFY `per_weightage` decimal(21,9) not null default 0, MODIFY `score` decimal(21,9) not null default 0, MODIFY `score_earned` decimal(21,9) not null default 0
2025-10-06 10:23:07,728 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal` MODIFY `goal_score_percentage` decimal(21,9) not null default 0, MODIFY `final_score` decimal(21,9) not null default 0, MODIFY `total_score` decimal(21,9) not null default 0, MODIFY `self_score` decimal(21,9) not null default 0, MODIFY `avg_feedback_score` decimal(21,9) not null default 0
2025-10-06 10:23:07,976 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-06 10:23:08,793 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-10-06 10:23:09,371 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-10-06 10:23:10,297 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-10-06 10:23:10,354 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `container_no_index`, DROP INDEX `net_weight_index`, DROP INDEX `gross_weight_index`
2025-10-06 10:23:10,739 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-06 10:23:11,056 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-06 10:23:11,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-10-06 10:23:11,924 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-10-06 10:23:12,497 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_volume` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-10-06 10:23:12,739 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-10-06 10:23:35,428 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,6) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0
