2025-09-30 13:01:34,166 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for working
2025-09-30 13:01:34,174 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 13:01:34,176 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for working
2025-09-30 13:01:34,186 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 13:01:34,195 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 13:01:34,199 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for working
2025-09-30 13:01:34,206 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 13:01:34,209 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 13:01:34,212 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-30 13:01:34,216 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 13:01:34,218 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 13:01:34,226 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for working
2025-09-30 13:01:34,229 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for working
2025-09-30 13:01:34,234 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-09-30 13:01:34,236 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for working
2025-09-30 13:01:34,241 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 13:01:34,253 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-09-30 13:01:34,255 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-30 13:01:34,257 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 13:01:34,259 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 13:01:34,263 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-09-30 13:01:34,267 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-30 13:02:34,440 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 13:02:34,451 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 13:02:34,453 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 13:02:34,466 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 13:02:34,477 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 13:02:34,497 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 13:02:34,499 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 13:02:34,507 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 13:02:34,528 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 13:02:34,529 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 13:02:34,538 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 13:02:34,543 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 13:02:34,546 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 13:02:34,568 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 13:03:34,598 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 13:03:34,607 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 13:03:34,621 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 13:03:34,640 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 13:03:34,649 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-09-30 13:03:34,652 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 13:03:34,658 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 13:03:34,659 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 13:03:34,669 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 13:03:34,672 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 13:03:34,679 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 13:03:34,682 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 13:03:34,688 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 13:03:34,707 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 13:03:34,712 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 14:01:54,008 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for working
2025-09-30 14:01:54,013 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-30 14:01:54,015 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 14:01:54,017 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for working
2025-09-30 14:01:54,021 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 14:01:54,022 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for working
2025-09-30 14:01:54,030 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for working
2025-09-30 14:01:54,032 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 14:01:54,036 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 14:01:54,038 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for working
2025-09-30 14:01:54,040 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for working
2025-09-30 14:01:54,041 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 14:01:54,050 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for working
2025-09-30 14:01:54,052 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 14:01:54,054 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-09-30 14:01:54,058 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 14:01:54,060 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-30 14:01:54,063 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.reset_cycle because it was found in queue for working
2025-09-30 14:01:54,065 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for working
2025-09-30 14:01:54,066 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 14:01:54,071 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 14:01:54,074 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-30 14:01:54,076 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-30 14:01:54,077 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-30 14:01:54,080 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 14:01:54,082 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for working
2025-09-30 14:01:54,084 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-09-30 14:01:54,087 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for working
2025-09-30 14:01:54,089 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for working
2025-09-30 14:01:54,094 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for working
2025-09-30 14:01:54,096 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 14:01:54,098 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 14:01:54,100 ERROR scheduler Skipped queueing frappe.pulse.client.send_queued_events because it was found in queue for working
2025-09-30 14:01:54,101 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for working
2025-09-30 14:01:54,104 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 14:01:54,107 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for working
2025-09-30 14:01:54,114 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-30 14:01:54,117 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 14:01:54,122 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for working
2025-09-30 14:01:54,127 ERROR scheduler Skipped queueing drive.api.permissions.auto_delete_expired_perms because it was found in queue for working
2025-09-30 14:01:54,132 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for working
2025-09-30 14:01:54,136 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-09-30 14:02:54,331 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 14:02:54,337 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 14:02:54,339 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 14:02:54,346 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 14:02:54,350 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 14:02:54,358 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 14:02:54,372 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 14:02:54,376 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 14:02:54,378 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 14:02:54,395 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 14:02:54,396 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 14:02:54,401 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 14:02:54,416 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 14:02:54,421 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 14:03:54,483 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 14:03:54,500 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 14:03:54,503 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 14:03:54,524 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 14:03:54,551 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 14:03:54,554 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 14:03:54,555 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 14:03:54,567 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 14:03:54,577 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 14:03:54,585 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 14:03:54,587 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 14:03:54,594 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 14:03:54,595 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 14:03:54,597 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 14:04:54,933 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 14:04:54,942 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 14:04:54,958 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 14:04:54,964 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 14:04:54,990 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 14:04:54,992 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 14:04:54,996 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 14:04:55,006 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 14:04:55,013 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 14:04:55,018 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 14:04:55,022 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 14:04:55,027 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 14:04:55,031 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 14:04:55,038 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 14:05:55,076 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 14:05:55,078 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 14:05:55,082 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 14:05:55,086 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-30 14:05:55,091 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 14:05:55,092 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-30 14:05:55,095 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-30 14:05:55,100 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 14:05:55,101 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 14:05:55,103 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-30 14:05:55,108 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 14:05:55,115 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 14:05:55,119 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 14:05:55,122 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 14:05:55,124 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 14:05:55,128 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 14:05:55,134 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 14:05:55,146 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-30 14:05:55,151 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-30 14:05:55,166 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 15:01:14,270 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-09-30 15:01:14,278 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-30 15:01:14,280 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for working
2025-09-30 15:01:14,282 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 15:01:14,291 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 15:01:14,293 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for working
2025-09-30 15:01:14,298 ERROR scheduler Skipped queueing drive.api.permissions.auto_delete_expired_perms because it was found in queue for working
2025-09-30 15:01:14,299 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 15:01:14,307 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.reset_cycle because it was found in queue for working
2025-09-30 15:01:14,314 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for working
2025-09-30 15:01:14,315 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for working
2025-09-30 15:01:14,316 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 15:01:14,318 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for working
2025-09-30 15:01:14,321 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 15:01:14,322 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for working
2025-09-30 15:01:14,327 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-09-30 15:01:14,329 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 15:01:14,331 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-30 15:01:14,336 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 15:01:14,341 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for working
2025-09-30 15:01:14,342 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for working
2025-09-30 15:01:14,346 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-30 15:01:14,347 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 15:01:14,349 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for working
2025-09-30 15:01:14,352 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-09-30 15:01:14,353 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 15:01:14,354 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 15:01:14,357 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 15:01:14,359 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for working
2025-09-30 15:01:14,364 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 15:01:14,371 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for working
2025-09-30 15:01:14,372 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-30 15:01:14,376 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for working
2025-09-30 15:01:14,378 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-30 15:01:14,379 ERROR scheduler Skipped queueing frappe.pulse.client.send_queued_events because it was found in queue for working
2025-09-30 15:01:14,381 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for working
2025-09-30 15:01:14,382 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for working
2025-09-30 15:01:14,384 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for working
2025-09-30 15:01:14,386 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for working
2025-09-30 15:01:14,388 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 15:01:14,391 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-30 15:01:14,392 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for working
2025-09-30 15:01:14,396 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for working
2025-09-30 15:01:14,403 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 15:02:14,706 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 15:02:14,748 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 15:02:14,749 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 15:02:14,765 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 15:02:14,770 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 15:02:14,777 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 15:02:14,779 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 15:02:14,786 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 15:02:14,790 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 15:02:14,795 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 15:02:14,800 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 15:02:14,806 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 15:02:14,818 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 15:02:14,823 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 15:03:15,111 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 15:03:15,116 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 15:03:15,118 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 15:03:15,120 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 15:03:15,123 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 15:03:15,162 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 15:03:15,178 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 15:03:15,195 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 15:03:15,205 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 15:03:15,223 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 15:03:15,235 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 15:03:15,239 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 15:03:15,243 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 15:03:15,246 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 15:03:15,249 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-09-30 16:01:35,708 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for working
2025-09-30 16:01:35,711 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-09-30 16:01:35,720 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for working
2025-09-30 16:01:35,725 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for working
2025-09-30 16:01:35,727 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for working
2025-09-30 16:01:35,733 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for working
2025-09-30 16:01:35,742 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-30 16:01:35,744 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 16:01:35,746 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-30 16:01:35,749 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for working
2025-09-30 16:01:35,751 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 16:01:35,754 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for working
2025-09-30 16:01:35,757 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 16:01:35,760 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for working
2025-09-30 16:01:35,761 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 16:01:35,768 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 16:01:35,771 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 16:01:35,774 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-09-30 16:01:35,779 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-09-30 16:01:35,785 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 16:01:35,787 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for working
2025-09-30 16:01:35,789 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for working
2025-09-30 16:01:35,791 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-30 16:01:35,795 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-30 16:01:35,797 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for working
2025-09-30 16:01:35,800 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.reset_cycle because it was found in queue for working
2025-09-30 16:01:35,803 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 16:01:35,808 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for working
2025-09-30 16:01:35,810 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 16:01:35,815 ERROR scheduler Skipped queueing frappe.pulse.client.send_queued_events because it was found in queue for working
2025-09-30 16:01:35,817 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 16:01:35,820 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-30 16:01:35,829 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for working
2025-09-30 16:01:35,831 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for working
2025-09-30 16:01:35,846 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 16:01:35,848 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-30 16:01:35,851 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 16:01:35,853 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 16:01:35,855 ERROR scheduler Skipped queueing drive.api.permissions.auto_delete_expired_perms because it was found in queue for working
2025-09-30 16:01:35,857 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for working
2025-09-30 16:01:35,859 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for working
2025-09-30 16:01:35,860 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 16:02:36,075 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 16:02:36,086 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 16:02:36,119 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 16:02:36,121 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 16:02:36,122 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 16:02:36,129 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 16:02:36,138 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 16:02:36,140 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 16:02:36,148 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 16:02:36,156 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 16:02:36,158 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 16:02:36,162 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 16:02:36,166 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 16:02:36,171 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 16:03:36,333 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 16:03:36,336 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 16:03:36,338 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 16:03:36,343 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 16:03:36,358 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 16:03:36,360 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 16:03:36,372 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 16:03:36,383 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 16:03:36,388 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 16:03:36,399 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 16:03:36,407 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 16:03:36,429 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 16:03:36,450 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 16:03:36,453 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 16:04:36,813 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 16:04:36,823 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 16:04:36,826 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 16:04:36,830 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 16:04:36,844 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 16:04:36,849 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 16:04:36,852 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 16:04:36,857 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-09-30 16:04:36,858 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 16:04:36,863 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 16:04:36,870 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 16:04:36,874 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 16:04:36,883 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 16:04:36,928 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 16:04:36,932 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 16:05:36,973 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-30 16:05:36,975 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-30 16:05:36,979 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 16:05:36,985 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-30 16:05:36,988 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 16:05:36,992 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 16:05:37,003 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-30 16:05:37,009 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 16:05:37,021 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 16:05:37,023 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-30 16:05:37,027 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-09-30 16:05:37,030 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 16:05:37,045 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 16:05:37,046 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 16:05:37,054 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 16:05:37,060 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 16:05:37,064 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 16:05:37,068 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 16:05:37,078 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 16:05:37,083 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 16:05:37,088 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-30 17:01:56,987 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for working
2025-09-30 17:01:56,997 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.reset_cycle because it was found in queue for working
2025-09-30 17:01:57,000 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 17:01:57,002 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-30 17:01:57,007 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-30 17:01:57,009 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for working
2025-09-30 17:01:57,015 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 17:01:57,019 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 17:01:57,020 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for working
2025-09-30 17:01:57,028 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 17:01:57,034 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 17:01:57,038 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-09-30 17:01:57,040 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for working
2025-09-30 17:01:57,049 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-30 17:01:57,050 ERROR scheduler Skipped queueing frappe.pulse.client.send_queued_events because it was found in queue for working
2025-09-30 17:01:57,052 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for working
2025-09-30 17:01:57,054 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for working
2025-09-30 17:01:57,057 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-09-30 17:01:57,060 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 17:01:57,062 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for working
2025-09-30 17:01:57,064 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-30 17:01:57,066 ERROR scheduler Skipped queueing drive.api.permissions.auto_delete_expired_perms because it was found in queue for working
2025-09-30 17:01:57,067 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for working
2025-09-30 17:01:57,072 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-09-30 17:01:57,079 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 17:01:57,080 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for working
2025-09-30 17:01:57,083 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 17:01:57,086 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 17:01:57,089 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for working
2025-09-30 17:01:57,090 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for working
2025-09-30 17:01:57,093 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 17:01:57,094 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 17:01:57,097 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-30 17:01:57,098 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for working
2025-09-30 17:01:57,100 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 17:01:57,103 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 17:01:57,105 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-30 17:01:57,107 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for working
2025-09-30 17:01:57,109 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for working
2025-09-30 17:01:57,112 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for working
2025-09-30 17:01:57,113 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 17:01:57,115 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for working
2025-09-30 17:01:57,117 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for working
2025-09-30 17:02:57,282 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 17:02:57,294 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 17:02:57,296 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 17:02:57,305 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 17:02:57,319 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 17:02:57,324 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 17:02:57,331 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 17:02:57,346 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 17:02:57,349 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 17:02:57,357 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 17:02:57,378 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 17:02:57,380 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 17:02:57,401 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 17:02:57,410 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 17:03:57,804 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 17:03:57,823 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 17:03:57,840 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 17:03:57,841 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 17:03:57,846 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 17:03:57,850 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 17:03:57,852 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 17:03:57,854 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 17:03:57,867 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 17:03:57,878 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 17:03:57,887 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 17:03:57,905 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 17:03:57,927 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 17:03:57,935 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 18:01:18,837 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for working
2025-09-30 18:01:18,845 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 18:01:18,856 ERROR scheduler Skipped queueing frappe.pulse.client.send_queued_events because it was found in queue for working
2025-09-30 18:01:18,865 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for working
2025-09-30 18:01:18,872 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 18:01:18,881 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-30 18:01:18,892 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 18:01:18,907 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for working
2025-09-30 18:01:18,914 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 18:01:18,921 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for working
2025-09-30 18:01:18,937 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-30 18:01:18,950 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-09-30 18:01:18,953 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for working
2025-09-30 18:01:18,963 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for working
2025-09-30 18:01:18,965 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 18:01:18,968 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for working
2025-09-30 18:01:18,972 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 18:01:18,978 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 18:01:18,983 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for working
2025-09-30 18:01:18,985 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for working
2025-09-30 18:01:18,987 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 18:01:18,989 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for working
2025-09-30 18:01:18,994 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 18:01:18,996 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 18:01:19,000 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 18:01:19,004 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-09-30 18:01:19,010 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for working
2025-09-30 18:01:19,018 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-30 18:01:19,020 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for working
2025-09-30 18:01:19,024 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 18:01:19,026 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for working
2025-09-30 18:01:19,028 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for working
2025-09-30 18:01:19,032 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-30 18:01:19,036 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 18:01:19,039 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 18:01:19,053 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-30 18:01:19,057 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-30 18:01:19,062 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for working
2025-09-30 18:01:19,065 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.reset_cycle because it was found in queue for working
2025-09-30 18:01:19,074 ERROR scheduler Skipped queueing drive.api.permissions.auto_delete_expired_perms because it was found in queue for working
2025-09-30 18:01:19,077 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-09-30 18:01:19,083 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for working
2025-09-30 18:02:19,148 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 18:02:19,156 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-30 18:02:19,159 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 18:02:19,174 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 18:02:19,176 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 18:02:19,187 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 18:02:19,192 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-09-30 18:02:19,198 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for working
2025-09-30 18:02:19,203 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for working
2025-09-30 18:02:19,205 ERROR scheduler Skipped queueing drive.api.permissions.auto_delete_expired_perms because it was found in queue for working
2025-09-30 18:02:19,206 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for working
2025-09-30 18:02:19,208 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for working
2025-09-30 18:02:19,210 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for working
2025-09-30 18:02:19,211 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-09-30 18:02:19,213 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 18:02:19,215 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 18:02:19,219 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 18:02:19,221 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for working
2025-09-30 18:02:19,222 ERROR scheduler Skipped queueing frappe.pulse.client.send_queued_events because it was found in queue for working
2025-09-30 18:02:19,225 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for working
2025-09-30 18:02:19,228 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for working
2025-09-30 18:02:19,231 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for working
2025-09-30 18:02:19,233 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for working
2025-09-30 18:02:19,235 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-30 18:02:19,240 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for working
2025-09-30 18:02:19,243 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.reset_cycle because it was found in queue for working
2025-09-30 18:02:19,250 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-30 18:02:19,258 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for working
2025-09-30 18:02:19,261 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 18:02:19,263 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 18:02:19,269 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-30 18:02:19,273 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 18:02:19,277 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 18:02:19,282 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-09-30 18:02:19,288 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for working
2025-09-30 18:02:19,296 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-30 18:02:19,303 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for working
2025-09-30 18:02:19,307 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-30 18:02:19,312 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 18:02:19,314 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for working
2025-09-30 18:02:19,315 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 18:02:19,317 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for working
2025-09-30 18:03:19,603 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 18:03:19,616 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 18:03:19,622 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 18:03:19,631 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 18:03:19,683 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 18:03:19,736 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 18:03:19,805 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 18:03:19,830 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 18:03:19,841 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 18:03:19,843 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 18:03:19,848 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 18:03:19,858 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 18:03:19,861 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 18:03:19,880 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 18:04:20,231 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 18:04:20,236 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 18:04:20,266 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 18:04:20,273 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 18:04:20,290 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 18:04:20,301 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 18:04:20,310 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 18:04:20,314 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 18:04:20,358 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 18:04:20,367 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 18:04:20,387 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 18:04:20,395 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 18:04:20,429 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 18:04:20,451 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 18:05:20,804 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 18:05:20,815 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 18:05:20,819 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-09-30 18:05:20,837 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 18:05:20,840 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 18:05:20,846 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 18:05:20,852 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-30 18:05:20,856 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 18:05:20,868 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 18:05:20,872 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 18:05:20,895 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-30 18:05:20,913 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-30 18:05:20,915 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 18:05:20,944 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 18:05:20,949 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 18:05:20,959 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 18:05:20,962 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-30 18:05:20,968 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-30 18:05:20,972 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 18:05:20,975 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 18:05:20,988 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-30 18:06:21,265 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 18:06:21,270 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 18:06:21,275 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 18:06:21,283 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-30 18:06:21,290 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 18:06:21,295 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-30 18:06:21,313 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 18:06:21,315 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-09-30 18:06:21,333 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-30 18:06:21,342 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 18:06:21,355 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 18:06:21,358 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 18:06:21,360 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 18:06:21,373 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 18:06:21,377 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 18:06:21,381 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 18:06:21,396 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-30 18:06:21,407 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-30 18:06:21,416 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 18:06:21,433 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-30 18:06:21,440 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 18:07:21,895 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-30 18:07:21,896 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-09-30 18:07:21,900 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 18:07:21,908 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 18:07:21,911 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-30 18:07:21,916 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 18:07:21,929 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 18:07:21,938 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-30 18:07:21,943 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 18:07:21,946 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 18:07:21,948 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 18:07:21,954 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 18:07:21,957 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-30 18:07:21,963 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 18:07:21,970 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 18:07:21,976 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-30 18:07:22,005 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-30 18:07:22,021 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 18:07:22,025 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 18:07:22,037 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 18:07:22,048 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 19:01:41,298 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-30 19:01:41,306 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-30 19:01:41,308 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-30 19:01:41,310 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-30 19:01:41,332 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-30 19:01:41,342 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-30 19:01:41,348 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-30 19:01:41,356 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-30 19:01:41,359 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-30 19:01:41,365 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-30 19:01:41,368 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-30 19:01:41,374 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-30 19:01:41,400 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-30 19:01:41,408 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-30 19:03:41,892 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-10-01 08:51:52,621 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-10-01 08:51:52,628 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-10-01 08:51:52,636 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for working
2025-10-01 08:51:52,641 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-10-01 08:51:52,650 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-10-01 08:51:52,656 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-10-01 08:51:52,660 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for working
2025-10-01 08:51:52,661 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-10-01 08:51:52,667 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for working
2025-10-01 08:51:52,669 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for working
2025-10-01 08:51:52,678 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-10-01 08:51:52,680 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for working
2025-10-01 08:51:52,683 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for working
2025-10-01 08:51:52,692 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for working
2025-10-01 08:51:52,694 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-10-01 08:51:52,697 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for working
2025-10-01 08:51:52,700 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-10-01 08:51:52,709 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-10-01 08:51:52,721 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for working
2025-10-01 08:51:52,723 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for working
2025-10-01 08:51:52,729 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for working
2025-10-01 08:51:52,731 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for working
