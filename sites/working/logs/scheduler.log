2025-10-06 10:04:57,825 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-10-06 10:04:57,831 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-10-06 10:04:57,840 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-10-06 10:04:57,841 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-10-06 10:05:58,030 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-10-06 10:05:58,056 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-10-06 10:05:58,059 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-10-06 10:05:58,076 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-10-06 10:05:58,082 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-10-06 10:05:58,085 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-10-06 10:05:58,089 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-10-06 10:05:58,092 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-10-06 10:05:58,094 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-10-06 10:05:58,102 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-10-06 10:05:58,105 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-10-06 10:05:58,110 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-10-06 10:05:58,136 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-10-06 10:05:58,142 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-10-06 10:06:58,342 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-10-06 10:06:58,352 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-10-06 10:06:58,360 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-10-06 10:06:58,363 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-10-06 10:06:58,371 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-10-06 10:06:58,385 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-10-06 10:06:58,394 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-10-06 10:06:58,430 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-10-06 10:06:58,431 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-10-06 10:06:58,436 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-10-06 10:06:58,443 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-10-06 10:06:58,446 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-10-06 10:06:58,458 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-10-06 10:06:58,459 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-10-06 10:07:58,910 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-10-06 10:07:58,914 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-10-06 10:07:58,922 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-10-06 10:07:58,937 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-10-06 10:07:58,942 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-10-06 10:07:58,946 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-10-06 10:07:58,948 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-10-06 10:07:58,961 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-10-06 10:07:58,968 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-10-06 10:07:58,988 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-10-06 10:07:58,992 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-10-06 10:07:59,022 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-10-06 10:07:59,056 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-10-06 10:07:59,058 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-10-06 10:07:59,060 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
