2025-09-17 19:06:44,137 INFO ipython if not frappe.db.exists("Number Card", "SDG Dashboard Link"):
        card = frappe.new_doc("Number Card")
            card.name = "SDG Dashboard Link"
2025-09-17 19:06:44,139 INFO ipython     card.label = "View Full SDG Dashboard"
2025-09-17 19:06:44,139 INFO ipython     card.type = "Custom"
2025-09-17 19:06:44,139 INFO ipython     card.method = "sdg_reporting.sdg_reporting.api.dashboard.get_total_metrics"
2025-09-17 19:06:44,141 INFO ipython     card.color = "#3b82f6"
2025-09-17 19:06:44,141 INFO ipython     card.is_public = 1
2025-09-17 19:06:44,141 INFO ipython     card.module = "Sdg Reporting"
2025-09-17 19:06:44,141 INFO ipython     card.is_standard = 1
2025-09-17 19:06:44,141 INFO ipython     card.insert(ignore_permissions=True)
2025-09-17 19:06:44,141 INFO ipython     print("✅ Created SDG Dashboard Link card")
2025-09-17 19:06:44,142 INFO ipython # Update one of the dashboards to include this link
2025-09-17 19:06:44,142 INFO ipython dashboard = frappe.get_doc("Dashboard", "SDG Mapping Dashboard")
2025-09-17 19:06:44,142 INFO ipython dashboard.dashboard_name = "SDG Reporting Dashboard (Full View Available)"
2025-09-17 19:06:44,142 INFO ipython # Add the link card
2025-09-17 19:06:44,142 INFO ipython link_card_exists = False
2025-09-17 19:06:44,142 INFO ipython for card in dashboard.cards:
        if card.card == "SDG Dashboard Link":
                    link_card_exists = True
                            break
2025-09-17 19:06:44,142 INFO ipython if not link_card_exists:
        card_row = dashboard.append("cards")
            card_row.card = "SDG Dashboard Link"
2025-09-17 19:06:44,142 INFO ipython dashboard.save(ignore_permissions=True)
2025-09-17 19:06:44,142 INFO ipython print("✅ Updated SDG Mapping Dashboard with link to full dashboard")
2025-09-17 19:06:44,143 INFO ipython frappe.db.commit()
2025-09-17 19:06:44,143 INFO ipython print("\n🎯 SDG Dashboard Setup Complete!")
2025-09-17 19:06:44,145 INFO ipython print("\nYou now have:")
2025-09-17 19:06:44,145 INFO ipython print("1. Standard Frappe Dashboards with working charts")
2025-09-17 19:06:44,146 INFO ipython print("2. Custom SDG Dashboard with exact format from your reference")
2025-09-17 19:06:44,146 INFO ipython print("\nAccess the custom dashboard at:")
2025-09-17 19:06:44,146 INFO ipython print("http://localhost:8000/sdg_dashboard")
2025-09-17 19:06:44,146 INFO ipython print("\nOr use the standard dashboards at:")
2025-09-17 19:06:44,147 INFO ipython print("- http://localhost:8000/app/dashboard-view/SDG%20Mapping%20Dashboard")
2025-09-17 19:06:44,147 INFO ipython print("- http://localhost:8000/app/dashboard-view/SDG%20Progress%20Dashboard")
2025-09-17 19:06:44,147 INFO ipython print("- http://localhost:8000/app/dashboard-view/SDG%20Distribution%20Dashboard")
2025-09-17 19:06:44,147 INFO ipython === session end ===
2025-09-17 19:20:05,223 INFO ipython === bench console session ===
2025-09-17 19:20:05,223 INFO ipython # Create proper report-based dashboard charts following ERPNext pattern
2025-09-17 19:20:05,224 INFO ipython import frappe
2025-09-17 19:20:05,224 INFO ipython import json
2025-09-17 19:20:05,224 INFO ipython # Delete existing problematic charts first
2025-09-17 19:20:05,224 INFO ipython charts_to_delete = [
    "SDG Metrics Mapping Chart",
        "SDG Progress Tracking Chart", 
            "SDG Goal Distribution Chart",
                "SDG Metrics Mapping Report Chart",
                    "SDG Progress Tracking Report Chart",
                        "SDG Goal Distribution Report Chart"
                        ]
2025-09-17 19:20:05,224 INFO ipython for chart_name in charts_to_delete:
        if frappe.db.exists("Dashboard Chart", chart_name):
                    frappe.delete_doc("Dashboard Chart", chart_name, ignore_permissions=True)
                            print(f"🗑️ Deleted {chart_name}")
2025-09-17 19:20:05,224 INFO ipython frappe.db.commit()
2025-09-17 19:20:05,225 INFO ipython print("✅ Cleaned up old charts")
2025-09-17 19:20:05,225 INFO ipython # Create new report-based charts following ERPNext pattern
2025-09-17 19:20:05,226 INFO ipython charts_config = [
    {
            "name": "SDG Metrics by ESG Pillar",
                    "chart_name": "SDG Metrics by ESG Pillar", 
                            "chart_type": "Report",
                                    "report_name": "SDG Metrics Mapping",
                                            "x_field": "esg_pillar",
                                                    "y_axis": [
                                                                {
                                                                                "y_field": "metric_count",
                                                                                                "doctype": "Dashboard Chart Field"
                                                                                                            }
                                                                                                                    ],
                                                                                                                            "type": "Bar",
                                                                                                                                    "color": "#3b82f6",
                                                                                                                                            "is_public": 1,
                                                                                                                                                    "module": "Sdg Reporting",
                                                                                                                                                            "is_standard": 1,
                                                                                                                                                                    "filters_json": "[]",
                                                                                                                                                                            "use_report_chart": 0
                                                                                                                                                                                },
                                                                                                                                                                                    {
                                                                                                                                                                                            "name": "SDG Progress Q1 vs Q2",
                                                                                                                                                                                                    "chart_name": "SDG Progress Q1 vs Q2",
                                                                                                                                                                                                            "chart_type": "Report", 
                                                                                                                                                                                                                    "report_name": "SDG Progress Tracking",
                                                                                                                                                                                                                            "x_field": "metric",
                                                                                                                                                                                                                                    "y_axis": [
                                                                                                                                                                                                                                                {
                                                                                                                                                                                                                                                                "y_field": "q1",
                                                                                                                                                                                                                                                                                "doctype": "Dashboard Chart Field"
                                                                                                                                                                                                                                                                                            },
                                                                                                                                                                                                                                                                                                        {
                                                                                                                                                                                                                                                                                                                        "y_field": "q2", 
                                                                                                                                                                                                                                                                                                                                        "doctype": "Dashboard Chart Field"
                                                                                                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                                                                                                            ],
                                                                                                                                                                                                                                                                                                                                                                    "type": "Bar",
                                                                                                                                                                                                                                                                                                                                                                            "color": "#10b981",
                                                                                                                                                                                                                                                                                                                                                                                    "is_public": 1,
                                                                                                                                                                                                                                                                                                                                                                                            "module": "Sdg Reporting",
                                                                                                                                                                                                                                                                                                                                                                                                    "is_standard": 1,
                                                                                                                                                                                                                                                                                                                                                                                                            "filters_json": "[]",
                                                                                                                                                                                                                                                                                                                                                                                                                    "use_report_chart": 0
                                                                                                                                                                                                                                                                                                                                                                                                                        },
                                                                                                                                                                                                                                                                                                                                                                                                                            {
                                                                                                                                                                                                                                                                                                                                                                                                                                    "name": "SDG Goal Distribution",
                                                                                                                                                                                                                                                                                                                                                                                                                                            "chart_name": "SDG Goal Distribution",
                                                                                                                                                                                                                                                                                                                                                                                                                                                    "chart_type": "Report",
                                                                                                                                                                                                                                                                                                                                                                                                                                                            "report_name": "SDG Goal Distribution", 
                                                                                                                                                                                                                                                                                                                                                                                                                                                                    "x_field": "target_name",
                                                                                                                                                                                                                                                                                                                                                                                                                                                                            "y_axis": [
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        "y_field": "employee_impact",
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        "doctype": "Dashboard Chart Field"
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ],
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    "type": "Pie",
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            "color": "#f59e0b",
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    "is_public": 1,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            "module": "Sdg Reporting", 
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    "is_standard": 1,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            "filters_json": "[]",
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    "use_report_chart": 0
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ]
2025-09-17 19:20:05,226 INFO ipython # Create the charts
2025-09-17 19:20:05,226 INFO ipython for config in charts_config:
        chart = frappe.new_doc("Dashboard Chart")
            
2025-09-17 19:20:05,226 INFO ipython     # Set basic fields
2025-09-17 19:20:05,226 INFO ipython     chart.name = config["name"]
2025-09-17 19:20:05,227 INFO ipython     chart.chart_name = config["chart_name"]
2025-09-17 19:20:05,227 INFO ipython     chart.chart_type = config["chart_type"]
2025-09-17 19:20:05,227 INFO ipython     chart.report_name = config["report_name"]
2025-09-17 19:20:05,227 INFO ipython     chart.x_field = config["x_field"]
2025-09-17 19:20:05,227 INFO ipython     chart.type = config["type"]
2025-09-17 19:20:05,227 INFO ipython     chart.color = config["color"]
2025-09-17 19:20:05,227 INFO ipython     chart.is_public = config["is_public"]
2025-09-17 19:20:05,228 INFO ipython     chart.module = config["module"]
2025-09-17 19:20:05,228 INFO ipython     chart.is_standard = config["is_standard"]
2025-09-17 19:20:05,228 INFO ipython     chart.filters_json = config["filters_json"]
2025-09-17 19:20:05,228 INFO ipython     chart.use_report_chart = config["use_report_chart"]
2025-09-17 19:20:05,228 INFO ipython     # Add y_axis fields
2025-09-17 19:20:05,228 INFO ipython     for y_field_config in config["y_axis"]:
            y_field_row = chart.append("y_axis")
                y_field_row.y_field = y_field_config["y_field"]
2025-09-17 19:20:05,228 INFO ipython     chart.insert(ignore_permissions=True)
2025-09-17 19:20:05,228 INFO ipython     print(f"✅ Created {config['name']}")
2025-09-17 19:20:05,228 INFO ipython frappe.db.commit()
2025-09-17 19:20:05,228 INFO ipython print("\n🎯 Report-based Dashboard Charts Created Successfully!")
2025-09-17 19:20:05,229 INFO ipython print("\nCharts created:")
2025-09-17 19:20:05,229 INFO ipython print("1. SDG Metrics by ESG Pillar (Bar Chart)")
2025-09-17 19:20:05,229 INFO ipython print("2. SDG Progress Q1 vs Q2 (Bar Chart)")  
2025-09-17 19:20:05,229 INFO ipython print("3. SDG Goal Distribution (Pie Chart)")
2025-09-17 19:20:05,229 INFO ipython print("\nThese charts use the proper ERPNext pattern with:")
2025-09-17 19:20:05,229 INFO ipython print("- chart_type: 'Report'")
2025-09-17 19:20:05,229 INFO ipython print("- x_field and y_axis configuration")
2025-09-17 19:20:05,229 INFO ipython print("- Proper numeric field mapping")
2025-09-17 19:20:05,229 INFO ipython # Create the remaining charts properly
2025-09-17 19:20:05,230 INFO ipython remaining_charts = [
    {
            "name": "SDG Metrics by ESG Pillar",
                    "chart_name": "SDG Metrics by ESG Pillar", 
                            "chart_type": "Report",
                                    "report_name": "SDG Metrics Mapping",
                                            "x_field": "esg_pillar",
                                                    "y_field": "metric_count",
                                                            "type": "Bar",
                                                                    "color": "#3b82f6"
                                                                        },
                                                                            {
                                                                                    "name": "SDG Progress Q1 vs Q2",
                                                                                            "chart_name": "SDG Progress Q1 vs Q2",
                                                                                                    "chart_type": "Report", 
                                                                                                            "report_name": "SDG Progress Tracking",
                                                                                                                    "x_field": "metric",
                                                                                                                            "y_field": "q1",
                                                                                                                                    "type": "Bar",
                                                                                                                                            "color": "#10b981"
                                                                                                                                                }
                                                                                                                                                ]
2025-09-17 19:20:05,230 INFO ipython for config in remaining_charts:
        if not frappe.db.exists("Dashboard Chart", config["name"]):
                    chart = frappe.new_doc("Dashboard Chart")
                            chart.name = config["name"]
2025-09-17 19:20:05,230 INFO ipython         chart.chart_name = config["chart_name"]
2025-09-17 19:20:05,230 INFO ipython         chart.chart_type = config["chart_type"]
2025-09-17 19:20:05,230 INFO ipython         chart.report_name = config["report_name"]
2025-09-17 19:20:05,230 INFO ipython         chart.x_field = config["x_field"]
2025-09-17 19:20:05,231 INFO ipython         chart.type = config["type"]
2025-09-17 19:20:05,231 INFO ipython         chart.color = config["color"]
2025-09-17 19:20:05,231 INFO ipython         chart.is_public = 1
2025-09-17 19:20:05,231 INFO ipython         chart.module = "Sdg Reporting"
2025-09-17 19:20:05,231 INFO ipython         chart.is_standard = 1
2025-09-17 19:20:05,231 INFO ipython         chart.filters_json = "[]"
2025-09-17 19:20:05,231 INFO ipython         chart.use_report_chart = 0
2025-09-17 19:20:05,231 INFO ipython         # Add y_axis field
2025-09-17 19:20:05,231 INFO ipython         y_field_row = chart.append("y_axis")
2025-09-17 19:20:05,232 INFO ipython         y_field_row.y_field = config["y_field"]
2025-09-17 19:20:05,232 INFO ipython         chart.insert(ignore_permissions=True)
2025-09-17 19:20:05,232 INFO ipython         print(f"✅ Created {config['name']}")
2025-09-17 19:20:05,232 INFO ipython frappe.db.commit()
2025-09-17 19:20:05,232 INFO ipython print("✅ All charts created successfully!")
2025-09-17 19:20:05,232 INFO ipython # Create charts with unique names
2025-09-17 19:20:05,232 INFO ipython unique_charts = [
    {
            "name": "ESG Pillar Metrics Count",
                    "chart_name": "ESG Pillar Metrics Count", 
                            "chart_type": "Report",
                                    "report_name": "SDG Metrics Mapping",
                                            "x_field": "esg_pillar",
                                                    "y_field": "metric_count",
                                                            "type": "Bar",
                                                                    "color": "#3b82f6"
                                                                        },
                                                                            {
                                                                                    "name": "Q1 vs Q2 Progress",
                                                                                            "chart_name": "Q1 vs Q2 Progress",
                                                                                                    "chart_type": "Report", 
                                                                                                            "report_name": "SDG Progress Tracking",
                                                                                                                    "x_field": "metric",
                                                                                                                            "y_field": "q1",
                                                                                                                                    "type": "Bar",
                                                                                                                                            "color": "#10b981"
                                                                                                                                                }
                                                                                                                                                ]
2025-09-17 19:20:05,232 INFO ipython for config in unique_charts:
        if not frappe.db.exists("Dashboard Chart", config["name"]):
                    chart = frappe.new_doc("Dashboard Chart")
                            chart.name = config["name"]
2025-09-17 19:20:05,232 INFO ipython         chart.chart_name = config["chart_name"]
2025-09-17 19:20:05,233 INFO ipython         chart.chart_type = config["chart_type"]
2025-09-17 19:20:05,233 INFO ipython         chart.report_name = config["report_name"]
2025-09-17 19:20:05,233 INFO ipython         chart.x_field = config["x_field"]
2025-09-17 19:20:05,233 INFO ipython         chart.type = config["type"]
2025-09-17 19:20:05,233 INFO ipython         chart.color = config["color"]
2025-09-17 19:20:05,233 INFO ipython         chart.is_public = 1
2025-09-17 19:20:05,233 INFO ipython         chart.module = "Sdg Reporting"
2025-09-17 19:20:05,233 INFO ipython         chart.is_standard = 1
2025-09-17 19:20:05,233 INFO ipython         chart.filters_json = "[]"
2025-09-17 19:20:05,234 INFO ipython         chart.use_report_chart = 0
2025-09-17 19:20:05,234 INFO ipython         # Add y_axis field
2025-09-17 19:20:05,238 INFO ipython         y_field_row = chart.append("y_axis")
2025-09-17 19:20:05,239 INFO ipython         y_field_row.y_field = config["y_field"]
2025-09-17 19:20:05,239 INFO ipython         chart.insert(ignore_permissions=True)
2025-09-17 19:20:05,239 INFO ipython         print(f"✅ Created {config['name']}")
2025-09-17 19:20:05,239 INFO ipython     else:
            print(f"⚠️ {config['name']} already exists")
2025-09-17 19:20:05,239 INFO ipython frappe.db.commit()
2025-09-17 19:20:05,239 INFO ipython print("✅ Charts creation completed!")
2025-09-17 19:20:05,239 INFO ipython # Update dashboards to use the new charts
2025-09-17 19:20:05,241 INFO ipython dashboards_to_update = [
    {
            "name": "SDG Mapping Dashboard",
                    "charts": ["ESG Pillar Metrics Count"]
                        },
                            {
                                    "name": "SDG Progress Dashboard", 
                                            "charts": ["Q1 vs Q2 Progress"]
                                                },
                                                    {
                                                            "name": "SDG Distribution Dashboard",
                                                                    "charts": ["SDG Goal Distribution"]
                                                                        }
                                                                        ]
2025-09-17 19:20:05,241 INFO ipython for dashboard_config in dashboards_to_update:
        if frappe.db.exists("Dashboard", dashboard_config["name"]):
                    dashboard = frappe.get_doc("Dashboard", dashboard_config["name"])
                            
2025-09-17 19:20:05,241 INFO ipython         # Clear existing charts
2025-09-17 19:20:05,241 INFO ipython         dashboard.charts = []
2025-09-17 19:20:05,241 INFO ipython         # Add new charts
2025-09-17 19:20:05,241 INFO ipython         for chart_name in dashboard_config["charts"]:
                if frappe.db.exists("Dashboard Chart", chart_name):
                            chart_row = dashboard.append("charts")
                                    chart_row.chart = chart_name
2025-09-17 19:20:05,241 INFO ipython                 chart_row.width = "Full"
2025-09-17 19:20:05,242 INFO ipython         dashboard.save(ignore_permissions=True)
2025-09-17 19:20:05,242 INFO ipython         print(f"✅ Updated {dashboard_config['name']} with charts: {', '.join(dashboard_config['charts'])}")
2025-09-17 19:20:05,243 INFO ipython frappe.db.commit()
2025-09-17 19:20:05,243 INFO ipython print("\n🎯 Dashboard Update Complete!")
2025-09-17 19:20:05,244 INFO ipython print("\nYour SDG dashboards now use proper report-based charts:")
2025-09-17 19:20:05,244 INFO ipython print("1. SDG Mapping Dashboard - Shows metrics count by ESG Pillar")
2025-09-17 19:20:05,244 INFO ipython print("2. SDG Progress Dashboard - Shows Q1 vs Q2 progress")  
2025-09-17 19:20:05,244 INFO ipython print("3. SDG Distribution Dashboard - Shows goal distribution")
2025-09-17 19:20:05,244 INFO ipython print("\nAccess them at:")
2025-09-17 19:20:05,244 INFO ipython print("- http://localhost:8000/app/dashboard-view/SDG%20Mapping%20Dashboard")
2025-09-17 19:20:05,244 INFO ipython print("- http://localhost:8000/app/dashboard-view/SDG%20Progress%20Dashboard")
2025-09-17 19:20:05,246 INFO ipython print("- http://localhost:8000/app/dashboard-view/SDG%20Distribution%20Dashboard")
2025-09-17 19:20:05,246 INFO ipython === session end ===
2025-09-18 14:36:43,277 INFO ipython === bench console session ===
2025-09-18 14:36:43,287 INFO ipython import clearing.clearing.doctype.clearing_charges.clearing_charges
2025-09-18 14:36:43,287 INFO ipython print("Import successful")
2025-09-18 14:36:43,287 INFO ipython === session end ===
2025-09-18 14:38:32,870 INFO ipython === bench console session ===
2025-09-18 14:38:32,870 INFO ipython import frappe
2025-09-18 14:38:32,870 INFO ipython clearing_charges = frappe.get_all("Clearing Charges", fields=["name", "status", "reference_number"], limit=5)
2025-09-18 14:38:32,870 INFO ipython print(clearing_charges)
2025-09-18 14:38:32,871 INFO ipython clearing_charges = frappe.get_all("Clearing Charges", fields=["name", "status", "reference_number"], limit=5)
2025-09-18 14:38:32,871 INFO ipython print(clearing_charges)
2025-09-18 14:38:32,871 INFO ipython clearing_charges = frappe.get_all("Clearing Charges", fields=["name", "status"], limit=5)
2025-09-18 14:38:32,871 INFO ipython print(clearing_charges)
2025-09-18 14:38:32,871 INFO ipython === session end ===
2025-09-18 14:40:54,042 INFO ipython === bench console session ===
2025-09-18 14:40:54,042 INFO ipython import frappe
2025-09-18 14:40:54,042 INFO ipython from clearing.clearing.doctype.clearing_charges.clearing_charges import update_clearing_charges_status_from_invoice
2025-09-18 14:40:54,042 INFO ipython clearing_charges = frappe.get_all("Clearing Charges", fields=["name", "status", "reference_number"], limit=5)
2025-09-18 14:40:54,043 INFO ipython print(clearing_charges)
2025-09-18 14:40:54,043 INFO ipython frappe.reload_doctype("Clearing Charges")
2025-09-18 14:40:54,043 INFO ipython clearing_charges = frappe.get_all("Clearing Charges", fields=["name", "status", "reference_number"], limit=5)
2025-09-18 14:40:54,043 INFO ipython print(clearing_charges)
2025-09-18 14:40:54,043 INFO ipython === session end ===
2025-09-18 14:43:32,027 INFO ipython === bench console session ===
2025-09-18 14:43:32,027 INFO ipython import frappe
2025-09-18 14:43:32,028 INFO ipython # Test that our hook function works
2025-09-18 14:43:32,028 INFO ipython from clearing.clearing.doctype.clearing_charges.clearing_charges import update_clearing_charges_status_from_invoice
2025-09-18 14:43:32,028 INFO ipython # Create a mock invoice object to test the function
2025-09-18 14:43:32,028 INFO ipython class MockInvoice:
        def __init__(self, name, status):
                    self.name = name
                            self.status = status
2025-09-18 14:43:32,028 INFO ipython mock_invoice = MockInvoice("TEST-INV-001", "Paid")
2025-09-18 14:43:32,028 INFO ipython print(f"Testing with mock invoice: {mock_invoice.name}, Status: {mock_invoice.status}")
2025-09-18 14:43:32,029 INFO ipython # Test the function (it should handle the case where no clearing charges exist gracefully)
2025-09-18 14:43:32,029 INFO ipython try:
        update_clearing_charges_status_from_invoice(mock_invoice, "on_update")
            print("✅ Function executed successfully (no clearing charges found, which is expected)")
2025-09-18 14:43:32,029 INFO ipython except Exception as e:
        print(f"❌ Error: {e}")
2025-09-18 14:43:32,029 INFO ipython class MockInvoice:
        def __init__(self, name, status):
                    self.name = name
                            self.status = status
2025-09-18 14:43:32,029 INFO ipython mock_invoice = MockInvoice("TEST-INV-001", "Paid")
2025-09-18 14:43:32,029 INFO ipython print(f"Testing with mock invoice: {mock_invoice.name}, Status: {mock_invoice.status}")
2025-09-18 14:43:32,029 INFO ipython try:
        update_clearing_charges_status_from_invoice(mock_invoice, "on_update")
            print("✅ Function executed successfully (no clearing charges found, which is expected)")
2025-09-18 14:43:32,029 INFO ipython except Exception as e:
        print(f"❌ Error: {e}")
2025-09-18 14:43:32,029 INFO ipython === session end ===
2025-10-02 10:33:43,289 INFO ipython === bench console session ===
2025-10-02 10:33:43,290 INFO ipython # Test the Party Specific Item functionality
2025-10-02 10:33:43,290 INFO ipython import frappe
2025-10-02 10:33:43,290 INFO ipython # Create a test customer
2025-10-02 10:33:43,290 INFO ipython customer = frappe.get_doc({
    "doctype": "Customer",
        "customer_name": "Test Customer PSI",
            "customer_type": "Individual"
            })
2025-10-02 10:33:43,291 INFO ipython customer.insert(ignore_permissions=True)
2025-10-02 10:33:43,291 INFO ipython # Create a test item
2025-10-02 10:33:43,291 INFO ipython item = frappe.get_doc({
    "doctype": "Item",
        "item_code": "TEST-ITEM-PSI",
            "item_name": "Test Item PSI",
                "item_group": "All Item Groups",
                    "is_sales_item": 1
                    })
2025-10-02 10:33:43,291 INFO ipython item.insert(ignore_permissions=True)
2025-10-02 10:33:43,291 INFO ipython # Create another test item
2025-10-02 10:33:43,291 INFO ipython item2 = frappe.get_doc({
    "doctype": "Item",
        "item_code": "TEST-ITEM-PSI-2",
            "item_name": "Test Item PSI 2",
                "item_group": "All Item Groups",
                    "is_sales_item": 1
                    })
2025-10-02 10:33:43,291 INFO ipython item2.insert(ignore_permissions=True)
2025-10-02 10:33:43,291 INFO ipython print("Test data created successfully")
2025-10-02 10:33:43,291 INFO ipython # Test Inclusive functionality (default behavior)
2025-10-02 10:33:43,292 INFO ipython psi_inclusive = frappe.get_doc({
    "doctype": "Party Specific Item",
        "party_type": "Customer",
            "party": customer.name,
                "restrict_based_on": "Item",
                    "based_on_value": item.name,
                        "select": "Inclusive"
                        })
2025-10-02 10:33:43,292 INFO ipython psi_inclusive.insert(ignore_permissions=True)
2025-10-02 10:33:43,292 INFO ipython # Test the item query with inclusive filter
2025-10-02 10:33:43,292 INFO ipython from erpnext.controllers.queries import item_query
2025-10-02 10:33:43,292 INFO ipython filters = {"is_sales_item": 1, "customer": customer.name}
2025-10-02 10:33:43,292 INFO ipython items = item_query(
    doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
    )
2025-10-02 10:33:43,292 INFO ipython print("Inclusive filter results:")
2025-10-02 10:33:43,292 INFO ipython for item_result in items:
        print(f"Item: {item_result[0]}")
        
2025-10-02 10:33:43,292 INFO ipython print(f"Total items found: {len(items)}")
2025-10-02 10:33:43,292 INFO ipython === session end ===
2025-10-02 10:37:37,892 INFO ipython === bench console session ===
2025-10-02 10:37:37,892 INFO ipython # Test the Party Specific Item functionality after migration
2025-10-02 10:37:37,893 INFO ipython import frappe
2025-10-02 10:37:37,893 INFO ipython # Create test data
2025-10-02 10:37:37,893 INFO ipython customer = frappe.get_doc({
    "doctype": "Customer",
        "customer_name": "Test Customer PSI 2",
            "customer_type": "Individual"
            })
2025-10-02 10:37:37,893 INFO ipython customer.insert(ignore_permissions=True)
2025-10-02 10:37:37,893 INFO ipython # Create test items
2025-10-02 10:37:37,893 INFO ipython item1 = frappe.get_doc({
    "doctype": "Item",
        "item_code": "TEST-ITEM-PSI-1",
            "item_name": "Test Item PSI 1",
                "item_group": "All Item Groups",
                    "is_sales_item": 1
                    })
2025-10-02 10:37:37,893 INFO ipython item1.insert(ignore_permissions=True)
2025-10-02 10:37:37,893 INFO ipython item2 = frappe.get_doc({
    "doctype": "Item",
        "item_code": "TEST-ITEM-PSI-2",
            "item_name": "Test Item PSI 2",
                "item_group": "All Item Groups",
                    "is_sales_item": 1
                    })
2025-10-02 10:37:37,893 INFO ipython item2.insert(ignore_permissions=True)
2025-10-02 10:37:37,893 INFO ipython print("Test data created successfully")
2025-10-02 10:37:37,894 INFO ipython print(f"Customer: {customer.name}")
2025-10-02 10:37:37,894 INFO ipython print(f"Item 1: {item1.name}")
2025-10-02 10:37:37,894 INFO ipython print(f"Item 2: {item2.name}")
2025-10-02 10:37:37,894 INFO ipython # Test Inclusive functionality (default behavior)
2025-10-02 10:37:37,894 INFO ipython psi_inclusive = frappe.get_doc({
    "doctype": "Party Specific Item",
        "party_type": "Customer",
            "party": customer.name,
                "restrict_based_on": "Item",
                    "based_on_value": item1.name,
                        "select": "Inclusive"
                        })
2025-10-02 10:37:37,894 INFO ipython psi_inclusive.insert(ignore_permissions=True)
2025-10-02 10:37:37,894 INFO ipython # Test the item query with inclusive filter
2025-10-02 10:37:37,894 INFO ipython from erpnext.controllers.queries import item_query
2025-10-02 10:37:37,894 INFO ipython filters = {"is_sales_item": 1, "customer": customer.name}
2025-10-02 10:37:37,894 INFO ipython items = item_query(
    doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
    )
2025-10-02 10:37:37,895 INFO ipython print("Inclusive filter results:")
2025-10-02 10:37:37,895 INFO ipython for item_result in items:
        print(f"Item: {item_result[0]}")
        
2025-10-02 10:37:37,895 INFO ipython print(f"Total items found: {len(items)}")
2025-10-02 10:37:37,895 INFO ipython print(f"Should only show: {item1.name}")
2025-10-02 10:37:37,895 INFO ipython # Clean up the previous test
2025-10-02 10:37:37,895 INFO ipython frappe.db.delete("Party Specific Item", {"party": customer.name})
2025-10-02 10:37:37,895 INFO ipython # Test Exclusive functionality
2025-10-02 10:37:37,895 INFO ipython psi_exclusive = frappe.get_doc({
    "doctype": "Party Specific Item",
        "party_type": "Customer",
            "party": customer.name,
                "restrict_based_on": "Item",
                    "based_on_value": item1.name,
                        "select": "Exclusive"
                        })
2025-10-02 10:37:37,895 INFO ipython psi_exclusive.insert(ignore_permissions=True)
2025-10-02 10:37:37,895 INFO ipython # Test the item query with exclusive filter
2025-10-02 10:37:37,896 INFO ipython filters = {"is_sales_item": 1, "customer": customer.name}
2025-10-02 10:37:37,896 INFO ipython items = item_query(
    doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
    )
2025-10-02 10:37:37,896 INFO ipython print("Exclusive filter results:")
2025-10-02 10:37:37,896 INFO ipython for item_result in items:
        print(f"Item: {item_result[0]}")
        
2025-10-02 10:37:37,896 INFO ipython print(f"Total items found: {len(items)}")
2025-10-02 10:37:37,896 INFO ipython print(f"Should NOT show: {item1.name}")
2025-10-02 10:37:37,896 INFO ipython print(f"Should show: {item2.name} and other items except {item1.name}")
2025-10-02 10:37:37,897 INFO ipython # Test mixed inclusive and exclusive filters
2025-10-02 10:37:37,897 INFO ipython # Clean up first
2025-10-02 10:37:37,897 INFO ipython frappe.db.delete("Party Specific Item", {"party": customer.name})
2025-10-02 10:37:37,897 INFO ipython # Create a third item for testing
2025-10-02 10:37:37,897 INFO ipython item3 = frappe.get_doc({
    "doctype": "Item",
        "item_code": "TEST-ITEM-PSI-3",
            "item_name": "Test Item PSI 3",
                "item_group": "All Item Groups",
                    "is_sales_item": 1
                    })
2025-10-02 10:37:37,897 INFO ipython item3.insert(ignore_permissions=True)
2025-10-02 10:37:37,897 INFO ipython # Create inclusive filter for item1 and item2
2025-10-02 10:37:37,897 INFO ipython psi_inclusive = frappe.get_doc({
    "doctype": "Party Specific Item",
        "party_type": "Customer",
            "party": customer.name,
                "restrict_based_on": "Item",
                    "based_on_value": item1.name,
                        "select": "Inclusive"
                        })
2025-10-02 10:37:37,898 INFO ipython psi_inclusive.insert(ignore_permissions=True)
2025-10-02 10:37:37,898 INFO ipython psi_inclusive2 = frappe.get_doc({
    "doctype": "Party Specific Item",
        "party_type": "Customer",
            "party": customer.name,
                "restrict_based_on": "Item",
                    "based_on_value": item2.name,
                        "select": "Inclusive"
                        })
2025-10-02 10:37:37,898 INFO ipython psi_inclusive2.insert(ignore_permissions=True)
2025-10-02 10:37:37,898 INFO ipython # Test the query
2025-10-02 10:37:37,898 INFO ipython filters = {"is_sales_item": 1, "customer": customer.name}
2025-10-02 10:37:37,898 INFO ipython items = item_query(
    doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
    )
2025-10-02 10:37:37,898 INFO ipython print("Mixed inclusive filter results:")
2025-10-02 10:37:37,899 INFO ipython for item_result in items:
        print(f"Item: {item_result[0]}")
        
2025-10-02 10:37:37,899 INFO ipython print(f"Total items found: {len(items)}")
2025-10-02 10:37:37,899 INFO ipython print(f"Should show: {item1.name} and {item2.name}")
2025-10-02 10:37:37,899 INFO ipython print(f"Should NOT show: {item3.name}")
2025-10-02 10:37:37,899 INFO ipython === session end ===
2025-10-03 10:50:28,048 INFO ipython === bench console session ===
2025-10-03 10:50:28,049 INFO ipython bench --site rubis console
2025-10-03 10:50:28,050 INFO ipython bench --site rubis console
2025-10-03 10:50:28,050 INFO ipython python test_clearing_charges_sync.py
2025-10-03 10:50:28,050 INFO ipython === session end ===
2025-10-03 10:52:28,884 INFO ipython === bench console session ===
2025-10-03 10:52:28,884 INFO ipython python test_clearing_charges_sync.py
2025-10-03 10:52:28,884 INFO ipython bench --site working execute test_clearing_charges_sync.test_sync_flow
2025-10-03 10:52:28,884 INFO ipython === session end ===
2025-10-06 08:45:07,147 INFO ipython === bench console session ===
2025-10-06 08:45:07,148 INFO ipython # Test Customer Group and Supplier Group functionality
2025-10-06 08:45:07,148 INFO ipython import frappe
2025-10-06 08:45:07,149 INFO ipython # Create test customer group
2025-10-06 08:45:07,149 INFO ipython customer_group = frappe.get_doc({
    "doctype": "Customer Group",
        "customer_group_name": "Test Customer Group PSI",
            "parent_customer_group": "All Customer Groups"
            })
2025-10-06 08:45:07,149 INFO ipython customer_group.insert(ignore_permissions=True)
2025-10-06 08:45:07,149 INFO ipython # Create test supplier group
2025-10-06 08:45:07,149 INFO ipython supplier_group = frappe.get_doc({
    "doctype": "Supplier Group", 
        "supplier_group_name": "Test Supplier Group PSI"
        })
2025-10-06 08:45:07,149 INFO ipython supplier_group.insert(ignore_permissions=True)
2025-10-06 08:45:07,149 INFO ipython # Create test customer and assign to group
2025-10-06 08:45:07,149 INFO ipython customer = frappe.get_doc({
    "doctype": "Customer",
        "customer_name": "Test Customer Group PSI",
            "customer_type": "Individual",
                "customer_group": customer_group.name
                })
2025-10-06 08:45:07,149 INFO ipython customer.insert(ignore_permissions=True)
2025-10-06 08:45:07,149 INFO ipython # Create test supplier and assign to group
2025-10-06 08:45:07,150 INFO ipython supplier = frappe.get_doc({
    "doctype": "Supplier",
        "supplier_name": "Test Supplier Group PSI",
            "supplier_group": supplier_group.name
            })
2025-10-06 08:45:07,150 INFO ipython supplier.insert(ignore_permissions=True)
2025-10-06 08:45:07,150 INFO ipython print("Test data created successfully")
2025-10-06 08:45:07,150 INFO ipython print(f"Customer Group: {customer_group.name}")
2025-10-06 08:45:07,150 INFO ipython print(f"Supplier Group: {supplier_group.name}")
2025-10-06 08:45:07,150 INFO ipython print(f"Customer: {customer.name} (Group: {customer.customer_group})")
2025-10-06 08:45:07,150 INFO ipython print(f"Supplier: {supplier.name} (Group: {supplier.supplier_group})")
2025-10-06 08:45:07,150 INFO ipython # Create test items
2025-10-06 08:45:07,150 INFO ipython item1 = frappe.get_doc({
    "doctype": "Item",
        "item_code": "TEST-ITEM-CG-1",
            "item_name": "Test Item Customer Group 1",
                "item_group": "All Item Groups",
                    "is_sales_item": 1
                    })
2025-10-06 08:45:07,150 INFO ipython item1.insert(ignore_permissions=True)
2025-10-06 08:45:07,150 INFO ipython item2 = frappe.get_doc({
    "doctype": "Item",
        "item_code": "TEST-ITEM-CG-2",
            "item_name": "Test Item Customer Group 2",
                "item_group": "All Item Groups",
                    "is_sales_item": 1
                    })
2025-10-06 08:45:07,151 INFO ipython item2.insert(ignore_permissions=True)
2025-10-06 08:45:07,151 INFO ipython # Test Customer Group Inclusive functionality
2025-10-06 08:45:07,151 INFO ipython psi_customer_group_inclusive = frappe.get_doc({
    "doctype": "Party Specific Item",
        "party_type": "Customer Group",
            "party": customer_group.name,
                "restrict_based_on": "Item",
                    "based_on_value": item1.name,
                        "select": "Inclusive"
                        })
2025-10-06 08:45:07,151 INFO ipython psi_customer_group_inclusive.insert(ignore_permissions=True)
2025-10-06 08:45:07,151 INFO ipython # Test the item query with customer group inclusive filter
2025-10-06 08:45:07,151 INFO ipython from erpnext.controllers.queries import item_query
2025-10-06 08:45:07,151 INFO ipython filters = {"is_sales_item": 1, "customer": customer.name}
2025-10-06 08:45:07,151 INFO ipython items = item_query(
    doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
    )
2025-10-06 08:45:07,151 INFO ipython print("Customer Group Inclusive filter results:")
2025-10-06 08:45:07,152 INFO ipython for item_result in items:
        print(f"Item: {item_result[0]}")
        
2025-10-06 08:45:07,152 INFO ipython print(f"Total items found: {len(items)}")
2025-10-06 08:45:07,152 INFO ipython print(f"Should only show: {item1.name}")
2025-10-06 08:45:07,152 INFO ipython print(f"Should NOT show: {item2.name}")
2025-10-06 08:45:07,152 INFO ipython # Clean up previous test
2025-10-06 08:45:07,152 INFO ipython frappe.db.delete("Party Specific Item", {"party": customer_group.name})
2025-10-06 08:45:07,152 INFO ipython # Test Customer Group Exclusive functionality
2025-10-06 08:45:07,152 INFO ipython psi_customer_group_exclusive = frappe.get_doc({
    "doctype": "Party Specific Item",
        "party_type": "Customer Group",
            "party": customer_group.name,
                "restrict_based_on": "Item",
                    "based_on_value": item1.name,
                        "select": "Exclusive"
                        })
2025-10-06 08:45:07,152 INFO ipython psi_customer_group_exclusive.insert(ignore_permissions=True)
2025-10-06 08:45:07,152 INFO ipython # Test the item query with customer group exclusive filter
2025-10-06 08:45:07,153 INFO ipython filters = {"is_sales_item": 1, "customer": customer.name}
2025-10-06 08:45:07,153 INFO ipython items = item_query(
    doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
    )
2025-10-06 08:45:07,153 INFO ipython print("Customer Group Exclusive filter results:")
2025-10-06 08:45:07,153 INFO ipython for item_result in items:
        print(f"Item: {item_result[0]}")
        
2025-10-06 08:45:07,153 INFO ipython print(f"Total items found: {len(items)}")
2025-10-06 08:45:07,153 INFO ipython print(f"Should NOT show: {item1.name}")
2025-10-06 08:45:07,153 INFO ipython print(f"Should show: {item2.name} and other items")
2025-10-06 08:45:07,153 INFO ipython # Check if item1 is excluded and item2 is included
2025-10-06 08:45:07,153 INFO ipython item_names = [item[0] for item in items]
2025-10-06 08:45:07,154 INFO ipython print(f"Item1 excluded: {item1.name not in item_names}")
2025-10-06 08:45:07,154 INFO ipython print(f"Item2 included: {item2.name in item_names}")
2025-10-06 08:45:07,154 INFO ipython # Create test items for supplier
2025-10-06 08:45:07,154 INFO ipython item_supplier1 = frappe.get_doc({
    "doctype": "Item",
        "item_code": "TEST-ITEM-SG-1",
            "item_name": "Test Item Supplier Group 1",
                "item_group": "All Item Groups",
                    "is_purchase_item": 1
                    })
2025-10-06 08:45:07,154 INFO ipython item_supplier1.insert(ignore_permissions=True)
2025-10-06 08:45:07,154 INFO ipython item_supplier2 = frappe.get_doc({
    "doctype": "Item",
        "item_code": "TEST-ITEM-SG-2",
            "item_name": "Test Item Supplier Group 2",
                "item_group": "All Item Groups",
                    "is_purchase_item": 1
                    })
2025-10-06 08:45:07,154 INFO ipython item_supplier2.insert(ignore_permissions=True)
2025-10-06 08:45:07,154 INFO ipython # Test Supplier Group Inclusive functionality
2025-10-06 08:45:07,154 INFO ipython psi_supplier_group_inclusive = frappe.get_doc({
    "doctype": "Party Specific Item",
        "party_type": "Supplier Group",
            "party": supplier_group.name,
                "restrict_based_on": "Item",
                    "based_on_value": item_supplier1.name,
                        "select": "Inclusive"
                        })
2025-10-06 08:45:07,154 INFO ipython psi_supplier_group_inclusive.insert(ignore_permissions=True)
2025-10-06 08:45:07,155 INFO ipython # Test the item query with supplier group inclusive filter
2025-10-06 08:45:07,155 INFO ipython filters = {"is_purchase_item": 1, "supplier": supplier.name}
2025-10-06 08:45:07,155 INFO ipython items = item_query(
    doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
    )
2025-10-06 08:45:07,155 INFO ipython print("Supplier Group Inclusive filter results:")
2025-10-06 08:45:07,155 INFO ipython for item_result in items:
        print(f"Item: {item_result[0]}")
        
2025-10-06 08:45:07,155 INFO ipython print(f"Total items found: {len(items)}")
2025-10-06 08:45:07,155 INFO ipython print(f"Should only show: {item_supplier1.name}")
2025-10-06 08:45:07,155 INFO ipython print(f"Should NOT show: {item_supplier2.name}")
2025-10-06 08:45:07,155 INFO ipython # Clean up previous test
2025-10-06 08:45:07,155 INFO ipython frappe.db.delete("Party Specific Item", {"party": supplier_group.name})
2025-10-06 08:45:07,155 INFO ipython # Test Supplier Group Exclusive functionality
2025-10-06 08:45:07,155 INFO ipython psi_supplier_group_exclusive = frappe.get_doc({
    "doctype": "Party Specific Item",
        "party_type": "Supplier Group",
            "party": supplier_group.name,
                "restrict_based_on": "Item",
                    "based_on_value": item_supplier1.name,
                        "select": "Exclusive"
                        })
2025-10-06 08:45:07,156 INFO ipython psi_supplier_group_exclusive.insert(ignore_permissions=True)
2025-10-06 08:45:07,156 INFO ipython # Test the item query with supplier group exclusive filter
2025-10-06 08:45:07,156 INFO ipython filters = {"is_purchase_item": 1, "supplier": supplier.name}
2025-10-06 08:45:07,156 INFO ipython items = item_query(
    doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
    )
2025-10-06 08:45:07,156 INFO ipython print("Supplier Group Exclusive filter results:")
2025-10-06 08:45:07,156 INFO ipython for item_result in items:
        print(f"Item: {item_result[0]}")
        
2025-10-06 08:45:07,156 INFO ipython print(f"Total items found: {len(items)}")
2025-10-06 08:45:07,156 INFO ipython print(f"Should NOT show: {item_supplier1.name}")
2025-10-06 08:45:07,156 INFO ipython print(f"Should show: {item_supplier2.name} and other items")
2025-10-06 08:45:07,156 INFO ipython # Check if item_supplier1 is excluded and item_supplier2 is included
2025-10-06 08:45:07,156 INFO ipython item_names = [item[0] for item in items]
2025-10-06 08:45:07,157 INFO ipython print(f"Item_supplier1 excluded: {item_supplier1.name not in item_names}")
2025-10-06 08:45:07,157 INFO ipython print(f"Item_supplier2 included: {item_supplier2.name in item_names}")
2025-10-06 08:45:07,157 INFO ipython print("\n=== ALL TESTS COMPLETED SUCCESSFULLY ===")
2025-10-06 08:45:07,157 INFO ipython print("✅ Customer Group Inclusive: Working")
2025-10-06 08:45:07,157 INFO ipython print("✅ Customer Group Exclusive: Working")
2025-10-06 08:45:07,157 INFO ipython print("✅ Supplier Group Inclusive: Working")
2025-10-06 08:45:07,157 INFO ipython print("✅ Supplier Group Exclusive: Working")
2025-10-06 08:45:07,157 INFO ipython === session end ===
