2025-09-25 12:30:45,529 INFO ipython print(f"Net total: {invoice.net_total}")
2025-09-25 12:30:45,531 INFO ipython print(f"Grand total: {invoice.grand_total}")
2025-09-25 12:30:45,531 INFO ipython # Check if there are any rounding adjustments
2025-09-25 12:30:45,531 INFO ipython print(f"\nRounding adjustment: {getattr(invoice, 'rounding_adjustment', 'N/A')}")
2025-09-25 12:30:45,531 INFO ipython print(f"Rounded total: {getattr(invoice, 'rounded_total', 'N/A')}")
2025-09-25 12:30:45,531 INFO ipython total_items = 0
2025-09-25 12:30:45,531 INFO ipython for i, item in enumerate(invoice.items):
        print(f"Item {i+1}: {item.item_code}")
            print(f"  Qty: {item.qty}, Rate: {item.rate}, Amount: {item.amount}")
2025-09-25 12:30:45,531 INFO ipython     total_items += item.amount
2025-09-25 12:30:45,531 INFO ipython print(f"\nTotal from items: {total_items}")
2025-09-25 12:30:45,532 INFO ipython print(f"Net total: {invoice.net_total}")
2025-09-25 12:30:45,532 INFO ipython print(f"Total taxes: {invoice.total_taxes_and_charges}")
2025-09-25 12:30:45,532 INFO ipython print(f"Grand total: {invoice.grand_total}")
2025-09-25 12:30:45,532 INFO ipython print(f"Calculation: {invoice.net_total} + {invoice.total_taxes_and_charges} = {invoice.net_total + invoice.total_taxes_and_charges}")
2025-09-25 12:30:45,532 INFO ipython # Check the exact difference
2025-09-25 12:30:45,532 INFO ipython difference = invoice.grand_total - (invoice.net_total + invoice.total_taxes_and_charges)
2025-09-25 12:30:45,532 INFO ipython print(f"Difference: {difference}")
2025-09-25 12:30:45,532 INFO ipython print("All items:")
2025-09-25 12:30:45,533 INFO ipython total_items = 0
2025-09-25 12:30:45,533 INFO ipython for i in range(len(invoice.items)):
        item = invoice.items[i]
            print(f"Item {i+1}: {item.item_code} - Qty: {item.qty}, Rate: {item.rate}, Amount: {item.amount}")
2025-09-25 12:30:45,533 INFO ipython     total_items += item.amount
2025-09-25 12:30:45,533 INFO ipython print(f"Total from all items: {total_items}")
2025-09-25 12:30:45,533 INFO ipython # Now let's check why the payment is 0.01 less
2025-09-25 12:30:45,533 INFO ipython print(f"\nPayment Analysis:")
2025-09-25 12:30:45,533 INFO ipython print(f"Grand Total: {invoice.grand_total}")
2025-09-25 12:30:45,533 INFO ipython print(f"Payment Amount: {payment.amount}")
2025-09-25 12:30:45,534 INFO ipython print(f"Difference: {invoice.grand_total - payment.amount}")
2025-09-25 12:30:45,534 INFO ipython # Check if there's any write-off or adjustment
2025-09-25 12:30:45,534 INFO ipython print(f"\nWrite-off details:")
2025-09-25 12:30:45,534 INFO ipython print(f"Write-off amount: {getattr(invoice, 'write_off_amount', 'N/A')}")
2025-09-25 12:30:45,534 INFO ipython print(f"Change amount: {getattr(invoice, 'change_amount', 'N/A')}")
2025-09-25 12:30:45,534 INFO ipython print(f"Outstanding amount: {invoice.outstanding_amount}")
2025-09-25 12:30:45,534 INFO ipython print("Checking each item individually:")
2025-09-25 12:30:45,534 INFO ipython print(f"Item 1: {invoice.items[0].item_code} - Amount: {invoice.items[0].amount}")
2025-09-25 12:30:45,535 INFO ipython print(f"Item 2: {invoice.items[1].item_code} - Amount: {invoice.items[1].amount}")
2025-09-25 12:30:45,535 INFO ipython print(f"Item 3: {invoice.items[2].item_code} - Amount: {invoice.items[2].amount}")
2025-09-25 12:30:45,535 INFO ipython total = invoice.items[0].amount + invoice.items[1].amount + invoice.items[2].amount
2025-09-25 12:30:45,535 INFO ipython print(f"Manual total: {total}")
2025-09-25 12:30:45,535 INFO ipython print(f"Net total from invoice: {invoice.net_total}")
2025-09-25 12:30:45,535 INFO ipython # Check if there are any discounts
2025-09-25 12:30:45,535 INFO ipython print(f"\nDiscount details:")
2025-09-25 12:30:45,535 INFO ipython print(f"Discount amount: {getattr(invoice, 'discount_amount', 'N/A')}")
2025-09-25 12:30:45,536 INFO ipython print(f"Additional discount percentage: {getattr(invoice, 'additional_discount_percentage', 'N/A')}")
2025-09-25 12:30:45,536 INFO ipython print(f"Apply discount on: {getattr(invoice, 'apply_discount_on', 'N/A')}")
2025-09-25 12:30:45,536 INFO ipython # Check taxes in detail
2025-09-25 12:30:45,536 INFO ipython print("Tax details:")
2025-09-25 12:30:45,536 INFO ipython if hasattr(invoice, 'taxes') and invoice.taxes:
        for i, tax in enumerate(invoice.taxes):
                    print(f"Tax {i+1}: {tax.description}")
                            print(f"  Rate: {tax.rate}%")
2025-09-25 12:30:45,536 INFO ipython         print(f"  Tax Amount: {tax.tax_amount}")
2025-09-25 12:30:45,536 INFO ipython         print(f"  Total: {tax.total}")
2025-09-25 12:30:45,536 INFO ipython         print("---")
2025-09-25 12:30:45,536 INFO ipython else:
        print("No taxes found")
2025-09-25 12:30:45,537 INFO ipython # Check the calculation flow
2025-09-25 12:30:45,537 INFO ipython print(f"\nCalculation flow:")
2025-09-25 12:30:45,537 INFO ipython print(f"Items total: {total}")
2025-09-25 12:30:45,537 INFO ipython print(f"Net total: {invoice.net_total}")
2025-09-25 12:30:45,537 INFO ipython print(f"Difference (discount?): {total - invoice.net_total}")
2025-09-25 12:30:45,537 INFO ipython print(f"Taxes: {invoice.total_taxes_and_charges}")
2025-09-25 12:30:45,537 INFO ipython print(f"Grand total: {invoice.grand_total}")
2025-09-25 12:30:45,537 INFO ipython print(f"Payment: {payment.amount}")
2025-09-25 12:30:45,537 INFO ipython print(f"Outstanding: {invoice.outstanding_amount}")
2025-09-25 12:30:45,538 INFO ipython # Check if this is a rounding issue in the payment calculation
2025-09-25 12:30:45,538 INFO ipython from frappe.utils import flt
2025-09-25 12:30:45,538 INFO ipython print(f"\nRounding check:")
2025-09-25 12:30:45,538 INFO ipython print(f"Grand total (flt): {flt(invoice.grand_total, 2)}")
2025-09-25 12:30:45,538 INFO ipython print(f"Payment (flt): {flt(payment.amount, 2)}")
2025-09-25 12:30:45,538 INFO ipython print(f"Difference (flt): {flt(invoice.grand_total - payment.amount, 2)}")
2025-09-25 12:30:45,538 INFO ipython print(f"Number of taxes: {len(invoice.taxes)}")
2025-09-25 12:30:45,538 INFO ipython if invoice.taxes:
        tax = invoice.taxes[0]
            print(f"Tax: {tax.description}")
2025-09-25 12:30:45,538 INFO ipython     print(f"Rate: {tax.rate}%")
2025-09-25 12:30:45,539 INFO ipython     print(f"Tax Amount: {tax.tax_amount}")
2025-09-25 12:30:45,539 INFO ipython # The issue is clear now:
2025-09-25 12:30:45,539 INFO ipython # Items total: 217,600.00
2025-09-25 12:30:45,539 INFO ipython # But the system calculates: Net total (184,406.78) + Taxes (33,193.22) = 217,600.00
2025-09-25 12:30:45,539 INFO ipython # This means the tax is calculated as INCLUSIVE, reducing the net total
2025-09-25 12:30:45,539 INFO ipython # But the payment was made for 217,599.99 instead of 217,600.00
2025-09-25 12:30:45,539 INFO ipython print(f"\nSUMMARY:")
2025-09-25 12:30:45,539 INFO ipython print(f"The issue is a 0.01 rounding difference in the payment amount.")
2025-09-25 12:30:45,539 INFO ipython print(f"Grand Total: {invoice.grand_total}")
2025-09-25 12:30:45,540 INFO ipython print(f"Payment: {payment.amount}")
2025-09-25 12:30:45,540 INFO ipython print(f"This creates an outstanding of 0.01 causing 'Partly Paid' status.")
2025-09-25 12:30:45,540 INFO ipython # Check if we can fix this with write-off
2025-09-25 12:30:45,540 INFO ipython print(f"\nPOS Profile write-off limit: {pos_profile.write_off_limit}")
2025-09-25 12:30:45,540 INFO ipython print(f"Current outstanding: {invoice.outstanding_amount}")
2025-09-25 12:30:45,540 INFO ipython print(f"Can be written off: {invoice.outstanding_amount <= pos_profile.write_off_limit}")
2025-09-25 12:30:45,540 INFO ipython === session end ===
2025-09-25 12:35:50,371 INFO ipython === bench console session ===
2025-09-25 12:35:50,371 INFO ipython # Fix the invoice by writing off the 0.01 outstanding amount
2025-09-25 12:35:50,371 INFO ipython invoice = frappe.get_doc("Sales Invoice", "SINV-JS-43264")
2025-09-25 12:35:50,372 INFO ipython print(f"Before fix:")
2025-09-25 12:35:50,372 INFO ipython print(f"Outstanding: {invoice.outstanding_amount}")
2025-09-25 12:35:50,372 INFO ipython print(f"Status: {invoice.status}")
2025-09-25 12:35:50,372 INFO ipython print(f"Write-off amount: {invoice.write_off_amount}")
2025-09-25 12:35:50,372 INFO ipython # Set write-off amount to the outstanding amount
2025-09-25 12:35:50,372 INFO ipython invoice.write_off_amount = invoice.outstanding_amount
2025-09-25 12:35:50,372 INFO ipython invoice.write_off_account = "Write Off - MPASL"  # From POS Profile
2025-09-25 12:35:50,372 INFO ipython invoice.write_off_cost_center = "Main - MPASL"   # From POS Profile
2025-09-25 12:35:50,372 INFO ipython # Save the changes
2025-09-25 12:35:50,372 INFO ipython invoice.save()
2025-09-25 12:35:50,373 INFO ipython print(f"\nAfter fix:")
2025-09-25 12:35:50,373 INFO ipython print(f"Outstanding: {invoice.outstanding_amount}")
2025-09-25 12:35:50,373 INFO ipython print(f"Status: {invoice.status}")
2025-09-25 12:35:50,373 INFO ipython print(f"Write-off amount: {invoice.write_off_amount}")
2025-09-25 12:35:50,373 INFO ipython # Since the invoice is submitted, we need to use db_set to update the fields
2025-09-25 12:35:50,373 INFO ipython # This bypasses the validation but should be used carefully
2025-09-25 12:35:50,373 INFO ipython # First, let's check if we can use the automatic write-off flag
2025-09-25 12:35:50,373 INFO ipython invoice = frappe.get_doc("Sales Invoice", "SINV-JS-43264")
2025-09-25 12:35:50,373 INFO ipython # Update the write-off fields directly in the database
2025-09-25 12:35:50,373 INFO ipython frappe.db.set_value("Sales Invoice", "SINV-JS-43264", {
    "write_off_amount": 0.01,
        "write_off_account": "Write Off - MPASL",
            "write_off_cost_center": "Main - MPASL",
                "write_off_outstanding_amount_automatically": 1
                })
2025-09-25 12:35:50,373 INFO ipython # Recalculate the outstanding amount and status
2025-09-25 12:35:50,374 INFO ipython invoice.reload()
2025-09-25 12:35:50,374 INFO ipython from erpnext.controllers.taxes_and_totals import calculate_taxes_and_totals
2025-09-25 12:35:50,374 INFO ipython invoice.calculate_outstanding_amount()
2025-09-25 12:35:50,374 INFO ipython invoice.set_status(update=True)
2025-09-25 12:35:50,374 INFO ipython print(f"After database update:")
2025-09-25 12:35:50,374 INFO ipython print(f"Outstanding: {invoice.outstanding_amount}")
2025-09-25 12:35:50,374 INFO ipython print(f"Status: {invoice.status}")
2025-09-25 12:35:50,374 INFO ipython print(f"Write-off amount: {invoice.write_off_amount}")
2025-09-25 12:35:50,374 INFO ipython # Commit the changes
2025-09-25 12:35:50,374 INFO ipython frappe.db.commit()
2025-09-25 12:35:50,374 INFO ipython # Let's manually recalculate the outstanding amount
2025-09-25 12:35:50,374 INFO ipython invoice = frappe.get_doc("Sales Invoice", "SINV-JS-43264")
2025-09-25 12:35:50,375 INFO ipython print(f"Current values:")
2025-09-25 12:35:50,375 INFO ipython print(f"Grand Total: {invoice.grand_total}")
2025-09-25 12:35:50,375 INFO ipython print(f"Paid Amount: {invoice.paid_amount}")
2025-09-25 12:35:50,375 INFO ipython print(f"Write-off Amount: {invoice.write_off_amount}")
2025-09-25 12:35:50,375 INFO ipython print(f"Outstanding: {invoice.outstanding_amount}")
2025-09-25 12:35:50,375 INFO ipython # Calculate the correct outstanding amount
2025-09-25 12:35:50,375 INFO ipython # Outstanding = Grand Total - Paid Amount - Write-off Amount
2025-09-25 12:35:50,375 INFO ipython new_outstanding = invoice.grand_total - invoice.paid_amount - invoice.write_off_amount
2025-09-25 12:35:50,375 INFO ipython print(f"Calculated Outstanding: {new_outstanding}")
2025-09-25 12:35:50,375 INFO ipython # Update the outstanding amount directly
2025-09-25 12:35:50,375 INFO ipython frappe.db.set_value("Sales Invoice", "SINV-JS-43264", "outstanding_amount", new_outstanding)
2025-09-25 12:35:50,375 INFO ipython # Reload and check status
2025-09-25 12:35:50,376 INFO ipython invoice.reload()
2025-09-25 12:35:50,376 INFO ipython invoice.set_status(update=True)
2025-09-25 12:35:50,376 INFO ipython print(f"\nAfter recalculation:")
2025-09-25 12:35:50,376 INFO ipython print(f"Outstanding: {invoice.outstanding_amount}")
2025-09-25 12:35:50,376 INFO ipython print(f"Status: {invoice.status}")
2025-09-25 12:35:50,376 INFO ipython frappe.db.commit()
2025-09-25 12:35:50,376 INFO ipython # Final verification
2025-09-25 12:35:50,376 INFO ipython invoice = frappe.get_doc("Sales Invoice", "SINV-JS-43264")
2025-09-25 12:35:50,376 INFO ipython print("=== FINAL STATUS ===")
2025-09-25 12:35:50,376 INFO ipython print(f"Invoice: {invoice.name}")
2025-09-25 12:35:50,376 INFO ipython print(f"Status: {invoice.status}")
2025-09-25 12:35:50,376 INFO ipython print(f"Grand Total: {invoice.grand_total}")
2025-09-25 12:35:50,376 INFO ipython print(f"Paid Amount: {invoice.paid_amount}")
2025-09-25 12:35:50,376 INFO ipython print(f"Write-off Amount: {invoice.write_off_amount}")
2025-09-25 12:35:50,376 INFO ipython print(f"Outstanding Amount: {invoice.outstanding_amount}")
2025-09-25 12:35:50,376 INFO ipython print(f"Write-off Account: {invoice.write_off_account}")
2025-09-25 12:35:50,377 INFO ipython print(f"Write-off Cost Center: {invoice.write_off_cost_center}")
2025-09-25 12:35:50,377 INFO ipython print("===================")
2025-09-25 12:35:50,377 INFO ipython === session end ===
2025-09-25 13:06:57,684 INFO ipython === bench console session ===
2025-09-25 13:06:57,684 INFO ipython # Analyze the new invoice PRK-POS-2025-11736
2025-09-25 13:06:57,684 INFO ipython invoice = frappe.get_doc("Sales Invoice", "PRK-POS-2025-11736")
2025-09-25 13:06:57,684 INFO ipython print("=== CURRENT STATUS ===")
2025-09-25 13:06:57,684 INFO ipython print(f"Invoice: {invoice.name}")
2025-09-25 13:06:57,684 INFO ipython print(f"Status: {invoice.status}")
2025-09-25 13:06:57,685 INFO ipython print(f"Grand Total: {invoice.grand_total}")
2025-09-25 13:06:57,685 INFO ipython print(f"Paid Amount: {invoice.paid_amount}")
2025-09-25 13:06:57,685 INFO ipython print(f"Change Amount: {invoice.change_amount}")
2025-09-25 13:06:57,685 INFO ipython print(f"Outstanding Amount: {invoice.outstanding_amount}")
2025-09-25 13:06:57,685 INFO ipython print(f"Write-off Amount: {invoice.write_off_amount}")
2025-09-25 13:06:57,685 INFO ipython print("=====================")
2025-09-25 13:06:57,685 INFO ipython # Check the calculation
2025-09-25 13:06:57,685 INFO ipython print(f"\nCalculation Analysis:")
2025-09-25 13:06:57,685 INFO ipython print(f"Grand Total: {invoice.grand_total}")
2025-09-25 13:06:57,685 INFO ipython print(f"Paid Amount: {invoice.paid_amount}")
2025-09-25 13:06:57,685 INFO ipython print(f"Change Amount: {invoice.change_amount}")
2025-09-25 13:06:57,686 INFO ipython print(f"Expected Outstanding: {invoice.grand_total - invoice.paid_amount + invoice.change_amount}")
2025-09-25 13:06:57,686 INFO ipython print(f"Actual Outstanding: {invoice.outstanding_amount}")
2025-09-25 13:06:57,686 INFO ipython # The issue: Change amount is creating the outstanding
2025-09-25 13:06:57,686 INFO ipython # Solution: Write off the 0.01 outstanding amount
2025-09-25 13:06:57,686 INFO ipython # Check available sites
2025-09-25 13:06:57,686 INFO ipython import os
2025-09-25 13:06:57,686 INFO ipython sites = [d for d in os.listdir('/home/<USER>/Desktop/frappe-bench/sites') if os.path.isdir(f'/home/<USER>/Desktop/frappe-bench/sites/{d}') and d not in ['common_site_config.json', 'assets']]
2025-09-25 13:06:57,686 INFO ipython print("Available sites:")
2025-09-25 13:06:57,686 INFO ipython for site in sites:
        print(f"- {site}")
        
2025-09-25 13:06:57,686 INFO ipython === session end ===
2025-10-01 12:30:37,878 INFO ipython === bench console session ===
2025-10-01 12:30:37,878 INFO ipython # Check current user roles
2025-10-01 12:30:37,878 INFO ipython frappe.get_roles()
2025-10-01 12:30:37,878 INFO ipython # Check if there are any workflow states for Clearing Charges
2025-10-01 12:30:37,878 INFO ipython frappe.get_all("Workflow State", filters={"parent": "Clearing Charges"}, fields=["*"])
2025-10-01 12:30:37,878 INFO ipython # Check if there's a workflow for Clearing Charges
2025-10-01 12:30:37,878 INFO ipython frappe.get_all("Workflow", filters={"document_type": "Clearing Charges"}, fields=["*"])
2025-10-01 12:30:37,879 INFO ipython # Check if Clearing Charges is submittable
2025-10-01 12:30:37,879 INFO ipython doc_meta = frappe.get_meta("Clearing Charges")
2025-10-01 12:30:37,879 INFO ipython print("Is submittable:", doc_meta.is_submittable)
2025-10-01 12:30:37,879 INFO ipython print("Has workflow:", doc_meta.has_workflow)
2025-10-01 12:30:37,879 INFO ipython # Check if there are any custom permissions
2025-10-01 12:30:37,879 INFO ipython custom_perms = frappe.get_all("Custom DocPerm", filters={"parent": "Clearing Charges"}, fields=["*"])
2025-10-01 12:30:37,879 INFO ipython print("Custom permissions:", custom_perms)
2025-10-01 12:30:37,879 INFO ipython # Let's check if there are any field-level restrictions
2025-10-01 12:30:37,879 INFO ipython # Get a sample Clearing Charges document to test
2025-10-01 12:30:37,879 INFO ipython sample_docs = frappe.get_all("Clearing Charges", limit=1)
2025-10-01 12:30:37,879 INFO ipython if sample_docs:
        doc = frappe.get_doc("Clearing Charges", sample_docs[0].name)
            print("Document status:", doc.status)
2025-10-01 12:30:37,880 INFO ipython     print("Document docstatus:", doc.docstatus)
2025-10-01 12:30:37,880 INFO ipython     # Check if user has write permission
2025-10-01 12:30:37,880 INFO ipython     has_write = frappe.has_permission("Clearing Charges", "write", doc=doc)
2025-10-01 12:30:37,880 INFO ipython     print("Has write permission:", has_write)
2025-10-01 12:30:37,880 INFO ipython else:
        print("No Clearing Charges documents found")
2025-10-01 12:30:37,880 INFO ipython sample_docs = frappe.get_all("Clearing Charges", limit=1)
2025-10-01 12:30:37,880 INFO ipython print("Found documents:", len(sample_docs))
2025-10-01 12:30:37,880 INFO ipython if sample_docs:
        doc = frappe.get_doc("Clearing Charges", sample_docs[0].name)
            print("Document status:", doc.status)
2025-10-01 12:30:37,880 INFO ipython     print("Document docstatus:", doc.docstatus)
2025-10-01 12:30:37,880 INFO ipython     has_write = frappe.has_permission("Clearing Charges", "write", doc=doc)
2025-10-01 12:30:37,880 INFO ipython     print("Has write permission:", has_write)
2025-10-01 12:30:37,880 INFO ipython doc = frappe.get_doc("Clearing Charges", sample_docs[0].name)
2025-10-01 12:30:37,880 INFO ipython print("Document status:", doc.status)
2025-10-01 12:30:37,881 INFO ipython print("Document docstatus:", doc.docstatus)
2025-10-01 12:30:37,881 INFO ipython has_write = frappe.has_permission("Clearing Charges", "write", doc=doc)
2025-10-01 12:30:37,881 INFO ipython print("Has write permission:", has_write)
2025-10-01 12:30:37,881 INFO ipython # Check if there are any field-level read_only settings
2025-10-01 12:30:37,881 INFO ipython meta = frappe.get_meta("Clearing Charges")
2025-10-01 12:30:37,881 INFO ipython for field in meta.fields:
        if field.read_only:
                    print(f"Field '{field.fieldname}' is read-only: {field.read_only}")
                    
2025-10-01 12:30:37,881 INFO ipython # Check if there are any depends_on conditions that might affect editability
2025-10-01 12:30:37,881 INFO ipython for field in meta.fields:
        if field.depends_on:
                    print(f"Field '{field.fieldname}' depends on: {field.depends_on}")
                            
2025-10-01 12:30:37,881 INFO ipython === session end ===
2025-10-01 12:32:14,055 INFO ipython === bench console session ===
2025-10-01 12:32:14,055 INFO ipython # Check what roles are needed for Clearing Charges
2025-10-01 12:32:14,056 INFO ipython doc_perms = frappe.get_all("DocPerm", filters={"parent": "Clearing Charges"}, fields=["role", "read", "write", "create", "delete"])
2025-10-01 12:32:14,056 INFO ipython print("DocPerm entries for Clearing Charges:")
2025-10-01 12:32:14,056 INFO ipython for perm in doc_perms:
        print(f"Role: {perm.role}, Read: {perm.read}, Write: {perm.write}, Create: {perm.create}, Delete: {perm.delete}")
        
2025-10-01 12:32:14,056 INFO ipython # Check current user
2025-10-01 12:32:14,056 INFO ipython print(f"\nCurrent user: {frappe.session.user}")
2025-10-01 12:32:14,056 INFO ipython # Check if current user has the required role
2025-10-01 12:32:14,056 INFO ipython user_roles = frappe.get_roles()
2025-10-01 12:32:14,056 INFO ipython print(f"User has System Manager role: {'System Manager' in user_roles}")
2025-10-01 12:32:14,056 INFO ipython # Check what clearing-related roles exist
2025-10-01 12:32:14,056 INFO ipython clearing_roles = frappe.get_all("Role", filters=[["name", "like", "%clear%"]], fields=["name"])
2025-10-01 12:32:14,057 INFO ipython print("Clearing-related roles:")
2025-10-01 12:32:14,057 INFO ipython for role in clearing_roles:
        print(f"- {role.name}")
        
2025-10-01 12:32:14,057 INFO ipython # Also check for TRA roles
2025-10-01 12:32:14,057 INFO ipython tra_roles = frappe.get_all("Role", filters=[["name", "like", "%TRA%"]], fields=["name"])
2025-10-01 12:32:14,057 INFO ipython print("\nTRA-related roles:")
2025-10-01 12:32:14,057 INFO ipython for role in tra_roles:
        print(f"- {role.name}")
        
2025-10-01 12:32:14,057 INFO ipython === session end ===
2025-10-01 12:33:15,836 INFO ipython === bench console session ===
2025-10-01 12:33:15,836 INFO ipython # Verify the new permissions have been applied
2025-10-01 12:33:15,836 INFO ipython doc_perms = frappe.get_all("DocPerm", filters={"parent": "Clearing Charges"}, fields=["role", "read", "write", "create", "delete"])
2025-10-01 12:33:15,837 INFO ipython print("Updated DocPerm entries for Clearing Charges:")
2025-10-01 12:33:15,837 INFO ipython for perm in doc_perms:
        print(f"Role: {perm.role}, Read: {perm.read}, Write: {perm.write}, Create: {perm.create}, Delete: {perm.delete}")
        
2025-10-01 12:33:15,837 INFO ipython === session end ===
2025-10-01 12:34:29,855 INFO ipython === bench console session ===
2025-10-01 12:34:29,856 INFO ipython doc_perms = frappe.get_all("DocPerm", filters={"parent": "Clearing Charges"}, fields=["role", "read", "write", "create", "delete"])
2025-10-01 12:34:29,856 INFO ipython print("Updated DocPerm entries for Clearing Charges:")
2025-10-01 12:34:29,856 INFO ipython for perm in doc_perms:
        print(f"Role: {perm.role}, Read: {perm.read}, Write: {perm.write}, Create: {perm.create}, Delete: {perm.delete}")
        
2025-10-01 12:34:29,856 INFO ipython # Test permissions for Clearing Agent role
2025-10-01 12:34:29,856 INFO ipython has_write_clearing_agent = frappe.has_permission("Clearing Charges", "write", user="Administrator")
2025-10-01 12:34:29,856 INFO ipython print(f"Administrator has write permission: {has_write_clearing_agent}")
2025-10-01 12:34:29,856 INFO ipython # Clear permissions cache to ensure fresh check
2025-10-01 12:34:29,856 INFO ipython frappe.clear_cache()
2025-10-01 12:34:29,856 INFO ipython === session end ===
2025-10-01 12:43:42,037 INFO ipython === bench console session ===
2025-10-01 12:43:42,037 INFO ipython # Check a sample clearing charges document to see its current status and reference_number
2025-10-01 12:43:42,037 INFO ipython sample_docs = frappe.get_all("Clearing Charges", limit=3, fields=["name", "status", "reference_number", "docstatus"])
2025-10-01 12:43:42,037 INFO ipython print("Sample Clearing Charges documents:")
2025-10-01 12:43:42,037 INFO ipython for doc in sample_docs:
        print(f"Name: {doc.name}, Status: {doc.status}, Reference: {doc.reference_number}, DocStatus: {doc.docstatus}")
        
2025-10-01 12:43:42,037 INFO ipython # Check if there are any with reference_number set
2025-10-01 12:43:42,037 INFO ipython docs_with_ref = frappe.get_all("Clearing Charges", filters=[["reference_number", "is", "set"]], fields=["name", "status", "reference_number", "docstatus"])
2025-10-01 12:43:42,038 INFO ipython print(f"\nDocuments with reference_number: {len(docs_with_ref)}")
2025-10-01 12:43:42,038 INFO ipython for doc in docs_with_ref:
        print(f"Name: {doc.name}, Status: {doc.status}, Reference: {doc.reference_number}, DocStatus: {doc.docstatus}")
        
2025-10-01 12:43:42,038 INFO ipython # Let's check if there's any validation that prevents editing when reference_number is set
2025-10-01 12:43:42,038 INFO ipython # Get a document with reference_number and try to understand why it's read-only
2025-10-01 12:43:42,038 INFO ipython doc_with_ref = frappe.get_doc("Clearing Charges", "CC-CF-2025-0724-00767")
2025-10-01 12:43:42,038 INFO ipython print(f"Document: {doc_with_ref.name}")
2025-10-01 12:43:42,038 INFO ipython print(f"Status: {doc_with_ref.status}")
2025-10-01 12:43:42,038 INFO ipython print(f"Reference: {doc_with_ref.reference_number}")
2025-10-01 12:43:42,038 INFO ipython print(f"DocStatus: {doc_with_ref.docstatus}")
2025-10-01 12:43:42,038 INFO ipython # Check if the linked Sales Invoice exists and its status
2025-10-01 12:43:42,038 INFO ipython if doc_with_ref.reference_number:
        try:
                    invoice = frappe.get_doc("Sales Invoice", doc_with_ref.reference_number)
                            print(f"Linked Invoice Status: {invoice.status}")
2025-10-01 12:43:42,038 INFO ipython         print(f"Linked Invoice DocStatus: {invoice.docstatus}")
2025-10-01 12:43:42,039 INFO ipython     except:
            print("Linked invoice not found or error accessing it")
2025-10-01 12:43:42,039 INFO ipython === session end ===
2025-10-02 10:33:43,289 INFO ipython === bench console session ===
2025-10-02 10:33:43,290 INFO ipython # Test the Party Specific Item functionality
2025-10-02 10:33:43,290 INFO ipython import frappe
2025-10-02 10:33:43,290 INFO ipython # Create a test customer
2025-10-02 10:33:43,290 INFO ipython customer = frappe.get_doc({
    "doctype": "Customer",
        "customer_name": "Test Customer PSI",
            "customer_type": "Individual"
            })
2025-10-02 10:33:43,291 INFO ipython customer.insert(ignore_permissions=True)
2025-10-02 10:33:43,291 INFO ipython # Create a test item
2025-10-02 10:33:43,291 INFO ipython item = frappe.get_doc({
    "doctype": "Item",
        "item_code": "TEST-ITEM-PSI",
            "item_name": "Test Item PSI",
                "item_group": "All Item Groups",
                    "is_sales_item": 1
                    })
2025-10-02 10:33:43,291 INFO ipython item.insert(ignore_permissions=True)
2025-10-02 10:33:43,291 INFO ipython # Create another test item
2025-10-02 10:33:43,291 INFO ipython item2 = frappe.get_doc({
    "doctype": "Item",
        "item_code": "TEST-ITEM-PSI-2",
            "item_name": "Test Item PSI 2",
                "item_group": "All Item Groups",
                    "is_sales_item": 1
                    })
2025-10-02 10:33:43,291 INFO ipython item2.insert(ignore_permissions=True)
2025-10-02 10:33:43,291 INFO ipython print("Test data created successfully")
2025-10-02 10:33:43,291 INFO ipython # Test Inclusive functionality (default behavior)
2025-10-02 10:33:43,292 INFO ipython psi_inclusive = frappe.get_doc({
    "doctype": "Party Specific Item",
        "party_type": "Customer",
            "party": customer.name,
                "restrict_based_on": "Item",
                    "based_on_value": item.name,
                        "select": "Inclusive"
                        })
2025-10-02 10:33:43,292 INFO ipython psi_inclusive.insert(ignore_permissions=True)
2025-10-02 10:33:43,292 INFO ipython # Test the item query with inclusive filter
2025-10-02 10:33:43,292 INFO ipython from erpnext.controllers.queries import item_query
2025-10-02 10:33:43,292 INFO ipython filters = {"is_sales_item": 1, "customer": customer.name}
2025-10-02 10:33:43,292 INFO ipython items = item_query(
    doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
    )
2025-10-02 10:33:43,292 INFO ipython print("Inclusive filter results:")
2025-10-02 10:33:43,292 INFO ipython for item_result in items:
        print(f"Item: {item_result[0]}")
        
2025-10-02 10:33:43,292 INFO ipython print(f"Total items found: {len(items)}")
2025-10-02 10:33:43,292 INFO ipython === session end ===
2025-10-02 10:37:37,892 INFO ipython === bench console session ===
2025-10-02 10:37:37,892 INFO ipython # Test the Party Specific Item functionality after migration
2025-10-02 10:37:37,893 INFO ipython import frappe
2025-10-02 10:37:37,893 INFO ipython # Create test data
2025-10-02 10:37:37,893 INFO ipython customer = frappe.get_doc({
    "doctype": "Customer",
        "customer_name": "Test Customer PSI 2",
            "customer_type": "Individual"
            })
2025-10-02 10:37:37,893 INFO ipython customer.insert(ignore_permissions=True)
2025-10-02 10:37:37,893 INFO ipython # Create test items
2025-10-02 10:37:37,893 INFO ipython item1 = frappe.get_doc({
    "doctype": "Item",
        "item_code": "TEST-ITEM-PSI-1",
            "item_name": "Test Item PSI 1",
                "item_group": "All Item Groups",
                    "is_sales_item": 1
                    })
2025-10-02 10:37:37,893 INFO ipython item1.insert(ignore_permissions=True)
2025-10-02 10:37:37,893 INFO ipython item2 = frappe.get_doc({
    "doctype": "Item",
        "item_code": "TEST-ITEM-PSI-2",
            "item_name": "Test Item PSI 2",
                "item_group": "All Item Groups",
                    "is_sales_item": 1
                    })
2025-10-02 10:37:37,893 INFO ipython item2.insert(ignore_permissions=True)
2025-10-02 10:37:37,893 INFO ipython print("Test data created successfully")
2025-10-02 10:37:37,894 INFO ipython print(f"Customer: {customer.name}")
2025-10-02 10:37:37,894 INFO ipython print(f"Item 1: {item1.name}")
2025-10-02 10:37:37,894 INFO ipython print(f"Item 2: {item2.name}")
2025-10-02 10:37:37,894 INFO ipython # Test Inclusive functionality (default behavior)
2025-10-02 10:37:37,894 INFO ipython psi_inclusive = frappe.get_doc({
    "doctype": "Party Specific Item",
        "party_type": "Customer",
            "party": customer.name,
                "restrict_based_on": "Item",
                    "based_on_value": item1.name,
                        "select": "Inclusive"
                        })
2025-10-02 10:37:37,894 INFO ipython psi_inclusive.insert(ignore_permissions=True)
2025-10-02 10:37:37,894 INFO ipython # Test the item query with inclusive filter
2025-10-02 10:37:37,894 INFO ipython from erpnext.controllers.queries import item_query
2025-10-02 10:37:37,894 INFO ipython filters = {"is_sales_item": 1, "customer": customer.name}
2025-10-02 10:37:37,894 INFO ipython items = item_query(
    doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
    )
2025-10-02 10:37:37,895 INFO ipython print("Inclusive filter results:")
2025-10-02 10:37:37,895 INFO ipython for item_result in items:
        print(f"Item: {item_result[0]}")
        
2025-10-02 10:37:37,895 INFO ipython print(f"Total items found: {len(items)}")
2025-10-02 10:37:37,895 INFO ipython print(f"Should only show: {item1.name}")
2025-10-02 10:37:37,895 INFO ipython # Clean up the previous test
2025-10-02 10:37:37,895 INFO ipython frappe.db.delete("Party Specific Item", {"party": customer.name})
2025-10-02 10:37:37,895 INFO ipython # Test Exclusive functionality
2025-10-02 10:37:37,895 INFO ipython psi_exclusive = frappe.get_doc({
    "doctype": "Party Specific Item",
        "party_type": "Customer",
            "party": customer.name,
                "restrict_based_on": "Item",
                    "based_on_value": item1.name,
                        "select": "Exclusive"
                        })
2025-10-02 10:37:37,895 INFO ipython psi_exclusive.insert(ignore_permissions=True)
2025-10-02 10:37:37,895 INFO ipython # Test the item query with exclusive filter
2025-10-02 10:37:37,896 INFO ipython filters = {"is_sales_item": 1, "customer": customer.name}
2025-10-02 10:37:37,896 INFO ipython items = item_query(
    doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
    )
2025-10-02 10:37:37,896 INFO ipython print("Exclusive filter results:")
2025-10-02 10:37:37,896 INFO ipython for item_result in items:
        print(f"Item: {item_result[0]}")
        
2025-10-02 10:37:37,896 INFO ipython print(f"Total items found: {len(items)}")
2025-10-02 10:37:37,896 INFO ipython print(f"Should NOT show: {item1.name}")
2025-10-02 10:37:37,896 INFO ipython print(f"Should show: {item2.name} and other items except {item1.name}")
2025-10-02 10:37:37,897 INFO ipython # Test mixed inclusive and exclusive filters
2025-10-02 10:37:37,897 INFO ipython # Clean up first
2025-10-02 10:37:37,897 INFO ipython frappe.db.delete("Party Specific Item", {"party": customer.name})
2025-10-02 10:37:37,897 INFO ipython # Create a third item for testing
2025-10-02 10:37:37,897 INFO ipython item3 = frappe.get_doc({
    "doctype": "Item",
        "item_code": "TEST-ITEM-PSI-3",
            "item_name": "Test Item PSI 3",
                "item_group": "All Item Groups",
                    "is_sales_item": 1
                    })
2025-10-02 10:37:37,897 INFO ipython item3.insert(ignore_permissions=True)
2025-10-02 10:37:37,897 INFO ipython # Create inclusive filter for item1 and item2
2025-10-02 10:37:37,897 INFO ipython psi_inclusive = frappe.get_doc({
    "doctype": "Party Specific Item",
        "party_type": "Customer",
            "party": customer.name,
                "restrict_based_on": "Item",
                    "based_on_value": item1.name,
                        "select": "Inclusive"
                        })
2025-10-02 10:37:37,898 INFO ipython psi_inclusive.insert(ignore_permissions=True)
2025-10-02 10:37:37,898 INFO ipython psi_inclusive2 = frappe.get_doc({
    "doctype": "Party Specific Item",
        "party_type": "Customer",
            "party": customer.name,
                "restrict_based_on": "Item",
                    "based_on_value": item2.name,
                        "select": "Inclusive"
                        })
2025-10-02 10:37:37,898 INFO ipython psi_inclusive2.insert(ignore_permissions=True)
2025-10-02 10:37:37,898 INFO ipython # Test the query
2025-10-02 10:37:37,898 INFO ipython filters = {"is_sales_item": 1, "customer": customer.name}
2025-10-02 10:37:37,898 INFO ipython items = item_query(
    doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
    )
2025-10-02 10:37:37,898 INFO ipython print("Mixed inclusive filter results:")
2025-10-02 10:37:37,899 INFO ipython for item_result in items:
        print(f"Item: {item_result[0]}")
        
2025-10-02 10:37:37,899 INFO ipython print(f"Total items found: {len(items)}")
2025-10-02 10:37:37,899 INFO ipython print(f"Should show: {item1.name} and {item2.name}")
2025-10-02 10:37:37,899 INFO ipython print(f"Should NOT show: {item3.name}")
2025-10-02 10:37:37,899 INFO ipython === session end ===
2025-10-03 10:50:28,048 INFO ipython === bench console session ===
2025-10-03 10:50:28,049 INFO ipython bench --site rubis console
2025-10-03 10:50:28,050 INFO ipython bench --site rubis console
2025-10-03 10:50:28,050 INFO ipython python test_clearing_charges_sync.py
2025-10-03 10:50:28,050 INFO ipython === session end ===
2025-10-03 10:52:28,884 INFO ipython === bench console session ===
2025-10-03 10:52:28,884 INFO ipython python test_clearing_charges_sync.py
2025-10-03 10:52:28,884 INFO ipython bench --site working execute test_clearing_charges_sync.test_sync_flow
2025-10-03 10:52:28,884 INFO ipython === session end ===
2025-10-06 08:45:07,147 INFO ipython === bench console session ===
2025-10-06 08:45:07,148 INFO ipython # Test Customer Group and Supplier Group functionality
2025-10-06 08:45:07,148 INFO ipython import frappe
2025-10-06 08:45:07,149 INFO ipython # Create test customer group
2025-10-06 08:45:07,149 INFO ipython customer_group = frappe.get_doc({
    "doctype": "Customer Group",
        "customer_group_name": "Test Customer Group PSI",
            "parent_customer_group": "All Customer Groups"
            })
2025-10-06 08:45:07,149 INFO ipython customer_group.insert(ignore_permissions=True)
2025-10-06 08:45:07,149 INFO ipython # Create test supplier group
2025-10-06 08:45:07,149 INFO ipython supplier_group = frappe.get_doc({
    "doctype": "Supplier Group", 
        "supplier_group_name": "Test Supplier Group PSI"
        })
2025-10-06 08:45:07,149 INFO ipython supplier_group.insert(ignore_permissions=True)
2025-10-06 08:45:07,149 INFO ipython # Create test customer and assign to group
2025-10-06 08:45:07,149 INFO ipython customer = frappe.get_doc({
    "doctype": "Customer",
        "customer_name": "Test Customer Group PSI",
            "customer_type": "Individual",
                "customer_group": customer_group.name
                })
2025-10-06 08:45:07,149 INFO ipython customer.insert(ignore_permissions=True)
2025-10-06 08:45:07,149 INFO ipython # Create test supplier and assign to group
2025-10-06 08:45:07,150 INFO ipython supplier = frappe.get_doc({
    "doctype": "Supplier",
        "supplier_name": "Test Supplier Group PSI",
            "supplier_group": supplier_group.name
            })
2025-10-06 08:45:07,150 INFO ipython supplier.insert(ignore_permissions=True)
2025-10-06 08:45:07,150 INFO ipython print("Test data created successfully")
2025-10-06 08:45:07,150 INFO ipython print(f"Customer Group: {customer_group.name}")
2025-10-06 08:45:07,150 INFO ipython print(f"Supplier Group: {supplier_group.name}")
2025-10-06 08:45:07,150 INFO ipython print(f"Customer: {customer.name} (Group: {customer.customer_group})")
2025-10-06 08:45:07,150 INFO ipython print(f"Supplier: {supplier.name} (Group: {supplier.supplier_group})")
2025-10-06 08:45:07,150 INFO ipython # Create test items
2025-10-06 08:45:07,150 INFO ipython item1 = frappe.get_doc({
    "doctype": "Item",
        "item_code": "TEST-ITEM-CG-1",
            "item_name": "Test Item Customer Group 1",
                "item_group": "All Item Groups",
                    "is_sales_item": 1
                    })
2025-10-06 08:45:07,150 INFO ipython item1.insert(ignore_permissions=True)
2025-10-06 08:45:07,150 INFO ipython item2 = frappe.get_doc({
    "doctype": "Item",
        "item_code": "TEST-ITEM-CG-2",
            "item_name": "Test Item Customer Group 2",
                "item_group": "All Item Groups",
                    "is_sales_item": 1
                    })
2025-10-06 08:45:07,151 INFO ipython item2.insert(ignore_permissions=True)
2025-10-06 08:45:07,151 INFO ipython # Test Customer Group Inclusive functionality
2025-10-06 08:45:07,151 INFO ipython psi_customer_group_inclusive = frappe.get_doc({
    "doctype": "Party Specific Item",
        "party_type": "Customer Group",
            "party": customer_group.name,
                "restrict_based_on": "Item",
                    "based_on_value": item1.name,
                        "select": "Inclusive"
                        })
2025-10-06 08:45:07,151 INFO ipython psi_customer_group_inclusive.insert(ignore_permissions=True)
2025-10-06 08:45:07,151 INFO ipython # Test the item query with customer group inclusive filter
2025-10-06 08:45:07,151 INFO ipython from erpnext.controllers.queries import item_query
2025-10-06 08:45:07,151 INFO ipython filters = {"is_sales_item": 1, "customer": customer.name}
2025-10-06 08:45:07,151 INFO ipython items = item_query(
    doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
    )
2025-10-06 08:45:07,151 INFO ipython print("Customer Group Inclusive filter results:")
2025-10-06 08:45:07,152 INFO ipython for item_result in items:
        print(f"Item: {item_result[0]}")
        
2025-10-06 08:45:07,152 INFO ipython print(f"Total items found: {len(items)}")
2025-10-06 08:45:07,152 INFO ipython print(f"Should only show: {item1.name}")
2025-10-06 08:45:07,152 INFO ipython print(f"Should NOT show: {item2.name}")
2025-10-06 08:45:07,152 INFO ipython # Clean up previous test
2025-10-06 08:45:07,152 INFO ipython frappe.db.delete("Party Specific Item", {"party": customer_group.name})
2025-10-06 08:45:07,152 INFO ipython # Test Customer Group Exclusive functionality
2025-10-06 08:45:07,152 INFO ipython psi_customer_group_exclusive = frappe.get_doc({
    "doctype": "Party Specific Item",
        "party_type": "Customer Group",
            "party": customer_group.name,
                "restrict_based_on": "Item",
                    "based_on_value": item1.name,
                        "select": "Exclusive"
                        })
2025-10-06 08:45:07,152 INFO ipython psi_customer_group_exclusive.insert(ignore_permissions=True)
2025-10-06 08:45:07,152 INFO ipython # Test the item query with customer group exclusive filter
2025-10-06 08:45:07,153 INFO ipython filters = {"is_sales_item": 1, "customer": customer.name}
2025-10-06 08:45:07,153 INFO ipython items = item_query(
    doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
    )
2025-10-06 08:45:07,153 INFO ipython print("Customer Group Exclusive filter results:")
2025-10-06 08:45:07,153 INFO ipython for item_result in items:
        print(f"Item: {item_result[0]}")
        
2025-10-06 08:45:07,153 INFO ipython print(f"Total items found: {len(items)}")
2025-10-06 08:45:07,153 INFO ipython print(f"Should NOT show: {item1.name}")
2025-10-06 08:45:07,153 INFO ipython print(f"Should show: {item2.name} and other items")
2025-10-06 08:45:07,153 INFO ipython # Check if item1 is excluded and item2 is included
2025-10-06 08:45:07,153 INFO ipython item_names = [item[0] for item in items]
2025-10-06 08:45:07,154 INFO ipython print(f"Item1 excluded: {item1.name not in item_names}")
2025-10-06 08:45:07,154 INFO ipython print(f"Item2 included: {item2.name in item_names}")
2025-10-06 08:45:07,154 INFO ipython # Create test items for supplier
2025-10-06 08:45:07,154 INFO ipython item_supplier1 = frappe.get_doc({
    "doctype": "Item",
        "item_code": "TEST-ITEM-SG-1",
            "item_name": "Test Item Supplier Group 1",
                "item_group": "All Item Groups",
                    "is_purchase_item": 1
                    })
2025-10-06 08:45:07,154 INFO ipython item_supplier1.insert(ignore_permissions=True)
2025-10-06 08:45:07,154 INFO ipython item_supplier2 = frappe.get_doc({
    "doctype": "Item",
        "item_code": "TEST-ITEM-SG-2",
            "item_name": "Test Item Supplier Group 2",
                "item_group": "All Item Groups",
                    "is_purchase_item": 1
                    })
2025-10-06 08:45:07,154 INFO ipython item_supplier2.insert(ignore_permissions=True)
2025-10-06 08:45:07,154 INFO ipython # Test Supplier Group Inclusive functionality
2025-10-06 08:45:07,154 INFO ipython psi_supplier_group_inclusive = frappe.get_doc({
    "doctype": "Party Specific Item",
        "party_type": "Supplier Group",
            "party": supplier_group.name,
                "restrict_based_on": "Item",
                    "based_on_value": item_supplier1.name,
                        "select": "Inclusive"
                        })
2025-10-06 08:45:07,154 INFO ipython psi_supplier_group_inclusive.insert(ignore_permissions=True)
2025-10-06 08:45:07,155 INFO ipython # Test the item query with supplier group inclusive filter
2025-10-06 08:45:07,155 INFO ipython filters = {"is_purchase_item": 1, "supplier": supplier.name}
2025-10-06 08:45:07,155 INFO ipython items = item_query(
    doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
    )
2025-10-06 08:45:07,155 INFO ipython print("Supplier Group Inclusive filter results:")
2025-10-06 08:45:07,155 INFO ipython for item_result in items:
        print(f"Item: {item_result[0]}")
        
2025-10-06 08:45:07,155 INFO ipython print(f"Total items found: {len(items)}")
2025-10-06 08:45:07,155 INFO ipython print(f"Should only show: {item_supplier1.name}")
2025-10-06 08:45:07,155 INFO ipython print(f"Should NOT show: {item_supplier2.name}")
2025-10-06 08:45:07,155 INFO ipython # Clean up previous test
2025-10-06 08:45:07,155 INFO ipython frappe.db.delete("Party Specific Item", {"party": supplier_group.name})
2025-10-06 08:45:07,155 INFO ipython # Test Supplier Group Exclusive functionality
2025-10-06 08:45:07,155 INFO ipython psi_supplier_group_exclusive = frappe.get_doc({
    "doctype": "Party Specific Item",
        "party_type": "Supplier Group",
            "party": supplier_group.name,
                "restrict_based_on": "Item",
                    "based_on_value": item_supplier1.name,
                        "select": "Exclusive"
                        })
2025-10-06 08:45:07,156 INFO ipython psi_supplier_group_exclusive.insert(ignore_permissions=True)
2025-10-06 08:45:07,156 INFO ipython # Test the item query with supplier group exclusive filter
2025-10-06 08:45:07,156 INFO ipython filters = {"is_purchase_item": 1, "supplier": supplier.name}
2025-10-06 08:45:07,156 INFO ipython items = item_query(
    doctype="Item", txt="", searchfield="name", start=0, page_len=20, filters=filters, as_dict=False
    )
2025-10-06 08:45:07,156 INFO ipython print("Supplier Group Exclusive filter results:")
2025-10-06 08:45:07,156 INFO ipython for item_result in items:
        print(f"Item: {item_result[0]}")
        
2025-10-06 08:45:07,156 INFO ipython print(f"Total items found: {len(items)}")
2025-10-06 08:45:07,156 INFO ipython print(f"Should NOT show: {item_supplier1.name}")
2025-10-06 08:45:07,156 INFO ipython print(f"Should show: {item_supplier2.name} and other items")
2025-10-06 08:45:07,156 INFO ipython # Check if item_supplier1 is excluded and item_supplier2 is included
2025-10-06 08:45:07,156 INFO ipython item_names = [item[0] for item in items]
2025-10-06 08:45:07,157 INFO ipython print(f"Item_supplier1 excluded: {item_supplier1.name not in item_names}")
2025-10-06 08:45:07,157 INFO ipython print(f"Item_supplier2 included: {item_supplier2.name in item_names}")
2025-10-06 08:45:07,157 INFO ipython print("\n=== ALL TESTS COMPLETED SUCCESSFULLY ===")
2025-10-06 08:45:07,157 INFO ipython print("✅ Customer Group Inclusive: Working")
2025-10-06 08:45:07,157 INFO ipython print("✅ Customer Group Exclusive: Working")
2025-10-06 08:45:07,157 INFO ipython print("✅ Supplier Group Inclusive: Working")
2025-10-06 08:45:07,157 INFO ipython print("✅ Supplier Group Exclusive: Working")
2025-10-06 08:45:07,157 INFO ipython === session end ===
