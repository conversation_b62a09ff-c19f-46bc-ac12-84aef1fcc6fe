2025-10-03 09:16:12,159 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-10-03 09:16:12,162 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for working
2025-10-03 09:16:12,163 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-10-03 09:16:12,164 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for working
2025-10-03 09:16:12,166 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-10-03 09:16:12,172 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-10-03 09:16:12,174 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-10-03 09:16:12,177 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-10-03 09:16:12,178 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for working
2025-10-03 09:16:12,179 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for working
2025-10-03 09:16:12,183 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for working
2025-10-03 09:16:12,185 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-10-03 09:16:12,187 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-10-03 09:16:12,188 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for working
2025-10-03 09:16:12,190 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for working
2025-10-03 09:17:12,233 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for working
2025-10-03 09:17:12,237 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-10-03 09:17:12,239 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-10-03 09:17:12,242 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for working
2025-10-03 09:17:12,244 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-10-03 09:17:12,245 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-10-03 09:17:12,247 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for working
2025-10-03 09:17:12,249 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for working
2025-10-03 09:17:12,251 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for working
2025-10-03 09:17:12,253 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for working
2025-10-03 09:17:12,255 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for working
2025-10-03 09:17:12,257 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-10-03 09:17:12,261 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-10-03 09:17:12,263 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-10-03 09:17:12,265 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for working
2025-10-03 09:17:12,269 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-10-03 09:17:12,270 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for working
2025-10-03 09:17:12,273 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-10-03 09:17:12,278 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-10-03 09:17:12,279 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-10-03 09:17:12,282 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for working
2025-10-03 09:17:12,294 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for working
2025-10-03 09:17:12,295 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-10-03 09:17:12,297 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for working
2025-10-03 09:17:12,298 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for working
2025-10-03 09:17:12,299 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for working
2025-10-03 09:17:12,301 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-10-03 09:17:12,305 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-10-03 09:17:12,307 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for working
2025-10-03 09:17:12,310 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for working
2025-10-03 09:17:12,313 ERROR scheduler Skipped queueing frappe.pulse.client.send_queued_events because it was found in queue for working
2025-10-03 09:17:12,316 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for working
2025-10-03 09:17:12,317 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for working
2025-10-03 09:17:12,319 ERROR scheduler Skipped queueing frappe.core.doctype.user_invitation.user_invitation.mark_expired_invitations because it was found in queue for working
2025-10-03 09:17:12,323 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for working
2025-10-03 09:17:12,326 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for working
2025-10-03 09:17:12,331 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-10-03 09:17:12,333 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for working
2025-10-03 09:17:12,336 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for working
2025-10-03 09:17:12,338 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for working
2025-10-03 09:17:12,341 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-10-03 09:17:12,345 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for working
2025-10-03 09:17:12,348 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-10-03 09:17:12,351 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for working
2025-10-03 09:17:12,354 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-10-03 09:17:12,355 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.reset_cycle because it was found in queue for working
2025-10-03 09:17:12,358 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for working
2025-10-03 09:17:12,360 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-10-03 09:17:12,361 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-10-03 09:17:12,365 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-10-03 09:17:12,367 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-10-03 09:17:12,369 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for working
2025-10-03 09:17:12,370 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-10-03 09:18:12,591 ERROR scheduler Skipped queueing frappe.pulse.client.send_queued_events because it was found in queue for working
2025-10-03 09:18:12,601 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-10-03 09:18:12,607 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for working
2025-10-03 09:18:12,612 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-10-03 09:18:12,615 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-10-03 09:18:12,625 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-10-03 09:18:12,627 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-10-03 09:18:12,634 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.reset_cycle because it was found in queue for working
2025-10-03 09:18:12,636 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for working
2025-10-03 09:18:12,640 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for working
2025-10-03 09:18:12,646 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-10-03 09:18:12,649 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-10-03 09:18:12,651 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-10-03 09:18:12,652 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-10-03 09:18:12,654 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for working
2025-10-03 09:18:12,658 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-10-03 09:18:12,660 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for working
2025-10-03 09:18:12,661 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-10-03 09:18:12,664 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-10-03 09:18:12,665 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for working
2025-10-03 09:18:12,666 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for working
2025-10-03 09:18:12,670 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-10-03 09:18:12,671 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-10-03 09:18:12,673 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for working
2025-10-03 09:18:12,675 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for working
2025-10-03 09:18:12,676 ERROR scheduler Skipped queueing frappe.core.doctype.user_invitation.user_invitation.mark_expired_invitations because it was found in queue for working
2025-10-03 09:18:12,678 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-10-03 09:18:12,679 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for working
2025-10-03 09:18:12,680 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for working
2025-10-03 09:18:12,684 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-10-03 09:18:12,687 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for working
2025-10-03 09:18:12,690 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for working
2025-10-03 09:18:12,692 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for working
2025-10-03 09:18:12,694 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for working
2025-10-03 09:18:12,696 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for working
2025-10-03 09:18:12,700 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-10-03 09:18:12,702 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-10-03 09:18:12,707 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for working
2025-10-03 09:18:12,710 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for working
2025-10-03 09:18:12,713 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-10-03 09:18:12,715 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for working
2025-10-03 09:18:12,717 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-10-03 09:18:12,719 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for working
2025-10-03 09:18:12,723 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for working
2025-10-03 09:18:12,728 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for working
2025-10-03 09:18:12,729 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for working
2025-10-03 09:18:12,731 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for working
2025-10-03 09:18:12,732 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-10-03 09:18:12,735 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for working
2025-10-03 09:18:12,737 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-10-03 09:18:12,738 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-10-03 09:18:12,750 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for working
2025-10-03 09:18:12,753 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for working
2025-10-03 09:19:13,191 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for working
2025-10-03 09:19:13,195 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-10-03 09:19:13,197 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-10-03 09:19:13,199 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-10-03 09:19:13,202 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-10-03 09:19:13,206 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for working
2025-10-03 09:19:13,208 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for working
2025-10-03 09:19:13,210 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for working
2025-10-03 09:19:13,212 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-10-03 09:19:13,214 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-10-03 09:19:13,217 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-10-03 09:19:13,219 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for working
2025-10-03 09:19:13,221 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for working
2025-10-03 09:19:13,223 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for working
2025-10-03 09:19:13,226 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for working
2025-10-03 09:19:13,230 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for working
2025-10-03 09:19:13,237 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for working
2025-10-03 09:19:13,240 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-10-03 09:19:13,242 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-10-03 09:19:13,245 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for working
2025-10-03 09:19:13,251 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-10-03 09:19:13,254 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-10-03 09:19:13,257 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-10-03 09:19:13,259 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for working
2025-10-03 09:19:13,264 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for working
2025-10-03 09:19:13,266 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for working
2025-10-03 09:19:13,268 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for working
2025-10-03 09:19:13,271 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-10-03 09:19:13,274 ERROR scheduler Skipped queueing frappe.core.doctype.user_invitation.user_invitation.mark_expired_invitations because it was found in queue for working
2025-10-03 09:19:13,276 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-10-03 09:19:13,280 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-10-03 09:19:13,282 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-10-03 09:19:13,284 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-10-03 09:19:13,285 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-10-03 09:19:13,287 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for working
2025-10-03 09:19:13,289 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for working
2025-10-03 09:19:13,291 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for working
2025-10-03 09:19:13,294 ERROR scheduler Skipped queueing frappe.pulse.client.send_queued_events because it was found in queue for working
2025-10-03 09:19:13,309 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-10-03 09:19:13,314 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-10-03 09:19:13,316 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.reset_cycle because it was found in queue for working
2025-10-03 09:19:13,319 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for working
2025-10-03 09:19:13,320 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for working
2025-10-03 09:19:13,323 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-10-03 09:19:13,325 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for working
2025-10-03 09:19:13,327 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-10-03 09:19:13,328 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for working
2025-10-03 09:19:13,329 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for working
2025-10-03 09:19:13,331 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for working
2025-10-03 09:19:13,333 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-10-03 09:19:13,336 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for working
2025-10-03 09:19:13,340 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for working
2025-10-03 09:19:13,343 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for working
2025-10-03 09:20:13,379 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-10-03 09:20:13,391 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for working
2025-10-03 09:20:13,396 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for working
2025-10-03 09:20:13,399 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-10-03 09:20:13,400 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-10-03 09:20:13,403 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-10-03 09:20:13,405 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for working
2025-10-03 09:20:13,411 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for working
2025-10-03 09:20:13,412 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for working
2025-10-03 09:20:13,419 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for working
2025-10-03 09:20:13,420 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-10-03 09:20:13,423 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for working
2025-10-03 09:20:13,426 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for working
2025-10-03 09:20:13,428 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-10-03 09:20:13,431 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for working
2025-10-03 09:20:13,432 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-10-03 09:20:13,436 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for working
2025-10-03 09:20:13,438 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for working
2025-10-03 09:20:13,439 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for working
2025-10-03 09:20:13,442 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-10-03 09:20:13,444 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for working
2025-10-03 09:20:13,446 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.reset_cycle because it was found in queue for working
2025-10-03 09:20:13,450 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-10-03 09:20:13,454 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-10-03 09:20:13,457 ERROR scheduler Skipped queueing frappe.pulse.client.send_queued_events because it was found in queue for working
2025-10-03 09:20:13,465 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for working
2025-10-03 09:20:13,466 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for working
2025-10-03 09:20:13,468 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for working
2025-10-03 09:20:13,470 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for working
2025-10-03 09:20:13,474 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for working
2025-10-03 09:20:13,475 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for working
2025-10-03 09:20:13,480 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-10-03 09:20:13,483 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for working
2025-10-03 09:20:13,487 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for working
2025-10-03 09:20:13,490 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-10-03 09:20:13,491 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for working
2025-10-03 09:20:13,493 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for working
2025-10-03 09:20:13,496 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for working
2025-10-03 09:20:13,500 ERROR scheduler Skipped queueing frappe.core.doctype.user_invitation.user_invitation.mark_expired_invitations because it was found in queue for working
2025-10-03 09:20:13,504 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-10-03 09:20:13,508 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for working
2025-10-03 09:20:13,511 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-10-03 09:20:13,519 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for working
2025-10-03 09:20:13,521 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-10-03 09:20:13,526 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for working
2025-10-03 09:20:13,528 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-10-03 09:21:13,763 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for working
2025-10-03 09:21:13,767 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-10-03 09:21:13,771 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-10-03 09:21:13,773 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for working
2025-10-03 09:21:13,776 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for working
2025-10-03 09:21:13,778 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-10-03 09:21:13,782 ERROR scheduler Skipped queueing frappe.pulse.client.send_queued_events because it was found in queue for working
2025-10-03 09:21:13,786 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for working
2025-10-03 09:21:13,788 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for working
2025-10-03 09:21:13,793 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-10-03 09:21:13,795 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.run_vehicle_batch because it was found in queue for working
2025-10-03 09:21:13,798 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-10-03 09:21:13,799 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for working
2025-10-03 09:21:13,803 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-10-03 09:21:13,805 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for working
2025-10-03 09:21:13,807 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for working
2025-10-03 09:21:13,810 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for working
2025-10-03 09:21:13,818 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for working
2025-10-03 09:21:13,819 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for working
2025-10-03 09:21:13,821 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for working
2025-10-03 09:21:13,825 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-10-03 09:21:13,829 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for working
2025-10-03 09:21:13,830 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-10-03 09:21:13,839 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for working
2025-10-03 09:21:13,843 ERROR scheduler Skipped queueing frappe.core.doctype.user_invitation.user_invitation.mark_expired_invitations because it was found in queue for working
2025-10-03 09:21:13,845 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for working
2025-10-03 09:21:13,847 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.reset_cycle because it was found in queue for working
2025-10-03 09:21:13,849 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-10-03 09:21:13,850 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for working
2025-10-03 09:21:13,856 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-10-03 09:21:13,861 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for working
2025-10-03 09:21:13,863 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-10-03 09:21:13,865 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for working
2025-10-03 09:21:13,867 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for working
2025-10-03 09:21:13,869 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for working
2025-10-03 09:21:13,873 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-10-03 09:21:13,875 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-10-03 09:21:13,877 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-10-03 09:21:13,879 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for working
2025-10-03 09:21:13,881 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-10-03 09:21:13,884 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for working
2025-10-03 09:21:13,892 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-10-03 09:21:13,898 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-10-03 09:21:13,902 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for working
2025-10-03 09:21:13,904 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for working
2025-10-03 09:21:13,906 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-10-03 09:21:13,908 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for working
2025-10-03 09:21:13,911 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-10-03 09:21:13,914 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for working
2025-10-03 09:21:13,916 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for working
2025-10-03 09:21:13,918 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-10-03 09:21:13,920 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-10-03 09:21:13,921 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-10-03 09:22:14,341 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-10-03 09:22:14,342 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for working
2025-10-03 09:22:14,351 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-10-03 09:22:14,352 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-10-03 09:22:14,354 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-10-03 09:22:14,357 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for working
2025-10-03 09:22:14,360 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for working
2025-10-03 09:22:14,362 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-10-03 09:22:14,364 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for working
2025-10-03 09:22:14,365 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for working
2025-10-03 09:22:14,367 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for working
2025-10-03 09:22:14,370 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for working
2025-10-03 09:22:14,372 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-10-03 09:22:14,377 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_sync_task.processor.reset_cycle because it was found in queue for working
2025-10-03 09:22:14,379 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-10-03 09:22:14,383 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for working
2025-10-03 09:22:14,387 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for working
2025-10-03 09:22:14,389 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for working
2025-10-03 09:22:14,393 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for working
2025-10-03 09:22:14,396 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for working
2025-10-03 09:22:14,401 ERROR scheduler Skipped queueing frappe.pulse.client.send_queued_events because it was found in queue for working
2025-10-03 09:22:14,404 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for working
2025-10-03 09:22:14,407 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for working
2025-10-03 09:22:14,409 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for working
2025-10-03 09:22:14,411 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-10-03 09:22:14,415 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-10-03 09:22:14,418 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-10-03 09:22:14,420 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for working
2025-10-03 09:22:14,422 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-10-03 09:22:14,425 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for working
2025-10-03 09:22:14,427 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for working
2025-10-03 09:22:14,428 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for working
2025-10-03 09:22:14,430 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-10-03 09:22:14,433 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for working
2025-10-03 09:22:14,434 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-10-03 09:22:14,437 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for working
2025-10-03 09:22:14,442 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for working
2025-10-03 09:22:14,444 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for working
2025-10-03 09:22:14,448 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for working
2025-10-03 09:22:14,451 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for working
2025-10-03 09:22:14,453 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for working
2025-10-03 09:22:14,457 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for working
2025-10-03 09:22:14,459 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-10-03 09:22:14,463 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-10-03 09:22:14,469 ERROR scheduler Skipped queueing frappe.core.doctype.user_invitation.user_invitation.mark_expired_invitations because it was found in queue for working
2025-10-03 09:22:14,470 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for working
2025-10-03 09:22:14,473 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
